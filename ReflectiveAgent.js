import { Agent } from './Agent.js';

/**
 * Tree-of-Thought Node Structure
 * Represents a single thought/reasoning step in the exploration tree
 */
class ThoughtNode {
  constructor(config) {
    this.id = config.id;
    this.parentId = config.parentId || null;
    this.thought = config.thought;
    this.action = config.action || null;
    this.result = config.result || null;
    this.score = config.score || 0;
    this.state = config.state || 'pending'; // pending, executing, complete, failed
    this.depth = config.depth || 0;
    this.timestamp = new Date();
    this.children = [];
    this.metadata = config.metadata || {};
  }

  /**
   * Add a child node to this thought
   * @param {ThoughtNode} childNode - The child node to add
   */
  addChild(childNode) {
    this.children.push(childNode);
    childNode.parentId = this.id;
    childNode.depth = this.depth + 1;
  }

  /**
   * Get the path from root to this node
   * @param {Map} nodeMap - Map of all nodes by ID
   * @returns {ThoughtNode[]} - Array of nodes from root to this node
   */
  getPath(nodeMap) {
    const path = [];
    let current = this;
    while (current) {
      path.unshift(current);
      current = current.parentId ? nodeMap.get(current.parentId) : null;
    }
    return path;
  }

  /**
   * Convert node to JSON for serialization
   * @returns {Object} - JSON representation of the node
   */
  toJSON() {
    return {
      id: this.id,
      parentId: this.parentId,
      thought: this.thought,
      action: this.action,
      result: this.result,
      score: this.score,
      state: this.state,
      depth: this.depth,
      timestamp: this.timestamp,
      metadata: this.metadata,
      childrenIds: this.children.map(child => child.id)
    };
  }
}

/**
 * ReflectiveAgent - Extends Agent with tree-of-thought reasoning capabilities
 * Provides self-reflective planning with multiple path exploration and scoring
 */
export class ReflectiveAgent extends Agent {
  constructor(config) {
    super(config);
    
    // Tree-of-thought specific configuration
    this.maxDepth = config.maxDepth || 5;
    this.maxBranches = config.maxBranches || 3;
    this.explorationStrategy = config.explorationStrategy || 'best-first';
    this.scoringThreshold = config.scoringThreshold || 6.0;
    
    // Current reasoning session state
    this.currentTree = null;
    this.nodeCounter = 0;
    this.activeNodes = new Map(); // Map of node ID to ThoughtNode
    
    // Memory integration for reasoning patterns
    this.reasoningMemoryKey = `reasoning_patterns_${this.id}`;
    
    // Enhanced event system for tree operations
    this.events.on('thoughtGenerated', this._onThoughtGenerated.bind(this));
    this.events.on('thoughtScored', this._onThoughtScored.bind(this));
    this.events.on('pathSelected', this._onPathSelected.bind(this));
  }

  /**
   * Execute reflective planning with tree-of-thought approach
   * @param {string} goal - The high-level goal to achieve
   * @param {Object} options - Configuration options for the reasoning process
   * @returns {Promise<Object>} - The final result and reasoning tree
   */
  async executeReflectivePlan(goal, options = {}) {
    const maxDepth = options.maxDepth || this.maxDepth;
    const context = options.context || {};
    
    // Initialize the reasoning tree
    this._initializeReasoningTree(goal);
    
    this.setStatus('reasoning');
    this.events.emit('reasoningStarted', {
      agent: this.id,
      goal,
      timestamp: new Date()
    });

    try {
      // Main reasoning loop
      for (let depth = 0; depth < maxDepth; depth++) {
        const currentNode = this._selectNextNode();
        
        if (!currentNode || currentNode.state === 'complete') {
          console.log(`Reasoning complete at depth ${depth}`);
          break;
        }

        console.log(`\n--- Reasoning Step ${depth + 1}: Exploring Node ${currentNode.id} ---`);
        console.log(`Current Thought: ${currentNode.thought}`);

        // Generate alternative thoughts/actions from current node
        const alternatives = await this._generateAlternatives(goal, currentNode, context);
        
        if (alternatives.length === 0) {
          console.log('No viable alternatives found, ending reasoning');
          break;
        }

        // Score each alternative
        const scoredAlternatives = await this._scoreAlternatives(goal, alternatives, currentNode);
        
        // Select and execute the best alternative
        const bestAlternative = this._selectBestAlternative(scoredAlternatives);
        
        if (bestAlternative) {
          const newNode = await this._executeAlternative(bestAlternative, currentNode);
          
          // Check for completion
          if (this._isGoalAchieved(newNode, goal)) {
            newNode.state = 'complete';
            break;
          }
        } else {
          console.log('No suitable alternatives found, ending reasoning');
          break;
        }
      }

      const result = this._extractFinalResult();
      
      // Store reasoning pattern in memory for future learning
      await this._storeReasoningPattern(goal, this.currentTree);
      
      this.setStatus('idle');
      this.events.emit('reasoningCompleted', {
        agent: this.id,
        goal,
        result,
        tree: this.currentTree,
        timestamp: new Date()
      });

      return result;

    } catch (error) {
      this.setStatus('error');
      this.events.emit('reasoningError', {
        agent: this.id,
        goal,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Initialize a new reasoning tree with the root node
   * @param {string} goal - The goal to achieve
   * @private
   */
  _initializeReasoningTree(goal) {
    this.nodeCounter = 0;
    this.activeNodes.clear();
    
    const rootNode = new ThoughtNode({
      id: this._generateNodeId(),
      thought: `Initial analysis of the goal: "${goal}". I need to break this down into actionable steps.`,
      state: 'pending',
      depth: 0,
      metadata: { isRoot: true, goal }
    });
    
    this.activeNodes.set(rootNode.id, rootNode);
    this.currentTree = {
      rootId: rootNode.id,
      goal,
      startTime: new Date(),
      nodes: this.activeNodes
    };
  }

  /**
   * Generate a unique node ID
   * @returns {string} - Unique node identifier
   * @private
   */
  _generateNodeId() {
    return `node_${this.id}_${++this.nodeCounter}_${Date.now()}`;
  }

  /**
   * Select the next node to explore based on the exploration strategy
   * @returns {ThoughtNode|null} - The next node to explore
   * @private
   */
  _selectNextNode() {
    const pendingNodes = Array.from(this.activeNodes.values())
      .filter(node => node.state === 'pending');
    
    if (pendingNodes.length === 0) return null;
    
    switch (this.explorationStrategy) {
      case 'best-first':
        return pendingNodes.reduce((best, current) => 
          current.score > best.score ? current : best
        );
      case 'depth-first':
        return pendingNodes.reduce((deepest, current) => 
          current.depth > deepest.depth ? current : deepest
        );
      case 'breadth-first':
        return pendingNodes.reduce((shallowest, current) => 
          current.depth < shallowest.depth ? current : shallowest
        );
      default:
        return pendingNodes[0];
    }
  }

  /**
   * Generate alternative thoughts/actions from the current node
   * @param {string} goal - The overall goal
   * @param {ThoughtNode} currentNode - The current node being explored
   * @param {Object} context - Additional context
   * @returns {Promise<Array>} - Array of alternative thoughts/actions
   * @private
   */
  async _generateAlternatives(goal, currentNode, context) {
    const path = currentNode.getPath(this.activeNodes);
    const pathSummary = path.map(node => ({
      thought: node.thought,
      action: node.action,
      result: node.result
    }));

    const prompt = this._buildAlternativesPrompt(goal, pathSummary, context);
    
    try {
      const response = await this.llmProvider.generateContent({
        contents: [{ role: "user", parts: [{ text: prompt }] }],
        config: {
          temperature: 0.8, // Higher temperature for more creative alternatives
          maxOutputTokens: 1024
        }
      });

      const responseText = response.candidates[0].content.parts[0].text;
      const alternatives = this._parseAlternatives(responseText);
      
      this.events.emit('thoughtGenerated', {
        agent: this.id,
        currentNode: currentNode.id,
        alternatives,
        timestamp: new Date()
      });

      return alternatives;

    } catch (error) {
      console.error('Error generating alternatives:', error);
      return [];
    }
  }
