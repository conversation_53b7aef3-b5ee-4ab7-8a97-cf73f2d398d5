import { Agent } from './Agent.js';

/**
 * Tree-of-Thought Node Structure
 * Represents a single thought/reasoning step in the exploration tree
 */
class ThoughtNode {
  constructor(config) {
    this.id = config.id;
    this.parentId = config.parentId || null;
    this.thought = config.thought;
    this.action = config.action || null;
    this.result = config.result || null;
    this.score = config.score || 0;
    this.state = config.state || 'pending'; // pending, executing, complete, failed
    this.depth = config.depth || 0;
    this.timestamp = new Date();
    this.children = [];
    this.metadata = config.metadata || {};
  }

  /**
   * Add a child node to this thought
   * @param {ThoughtNode} childNode - The child node to add
   */
  addChild(childNode) {
    this.children.push(childNode);
    childNode.parentId = this.id;
    childNode.depth = this.depth + 1;
  }

  /**
   * Get the path from root to this node
   * @param {Map} nodeMap - Map of all nodes by ID
   * @returns {ThoughtNode[]} - Array of nodes from root to this node
   */
  getPath(nodeMap) {
    const path = [];
    let current = this;
    while (current) {
      path.unshift(current);
      current = current.parentId ? nodeMap.get(current.parentId) : null;
    }
    return path;
  }

  /**
   * Convert node to JSON for serialization
   * @returns {Object} - JSON representation of the node
   */
  toJSON() {
    return {
      id: this.id,
      parentId: this.parentId,
      thought: this.thought,
      action: this.action,
      result: this.result,
      score: this.score,
      state: this.state,
      depth: this.depth,
      timestamp: this.timestamp,
      metadata: this.metadata,
      childrenIds: this.children.map(child => child.id)
    };
  }
}

/**
 * ReflectiveAgent - Extends Agent with tree-of-thought reasoning capabilities
 * Provides self-reflective planning with multiple path exploration and scoring
 */
export class ReflectiveAgent extends Agent {
  constructor(config) {
    super(config);
    
    // Tree-of-thought specific configuration
    this.maxDepth = config.maxDepth || 5;
    this.maxBranches = config.maxBranches || 3;
    this.explorationStrategy = config.explorationStrategy || 'best-first';
    this.scoringThreshold = config.scoringThreshold || 6.0;
    
    // Current reasoning session state
    this.currentTree = null;
    this.nodeCounter = 0;
    this.activeNodes = new Map(); // Map of node ID to ThoughtNode
    
    // Memory integration for reasoning patterns
    this.reasoningMemoryKey = `reasoning_patterns_${this.id}`;
    
    // Enhanced event system for tree operations
    this.events.on('thoughtGenerated', this._onThoughtGenerated.bind(this));
    this.events.on('thoughtScored', this._onThoughtScored.bind(this));
    this.events.on('pathSelected', this._onPathSelected.bind(this));

    // Parallel exploration configuration
    this.parallelExploration = config.parallelExploration !== false;
    this.maxParallelPaths = config.maxParallelPaths || 2;
  }

  /**
   * Execute reflective planning with tree-of-thought approach
   * @param {string} goal - The high-level goal to achieve
   * @param {Object} options - Configuration options for the reasoning process
   * @returns {Promise<Object>} - The final result and reasoning tree
   */
  async executeReflectivePlan(goal, options = {}) {
    const maxDepth = options.maxDepth || this.maxDepth;
    const context = options.context || {};
    
    // Initialize the reasoning tree
    this._initializeReasoningTree(goal);
    
    this.setStatus('reasoning');
    this.events.emit('reasoningStarted', {
      agent: this.id,
      goal,
      timestamp: new Date()
    });

    try {
      // Main reasoning loop
      for (let depth = 0; depth < maxDepth; depth++) {
        const currentNode = this._selectNextNode();
        
        if (!currentNode || currentNode.state === 'complete') {
          console.log(`Reasoning complete at depth ${depth}`);
          break;
        }

        console.log(`\n--- Reasoning Step ${depth + 1}: Exploring Node ${currentNode.id} ---`);
        console.log(`Current Thought: ${currentNode.thought}`);

        // Generate alternative thoughts/actions from current node
        const alternatives = await this._generateAlternatives(goal, currentNode, context);
        
        if (alternatives.length === 0) {
          console.log('No viable alternatives found, ending reasoning');
          break;
        }

        // Score each alternative
        const scoredAlternatives = await this._scoreAlternatives(goal, alternatives, currentNode);
        
        // Execute alternatives (parallel or sequential based on configuration)
        if (this.parallelExploration && scoredAlternatives.length > 1) {
          const executedNodes = await this._executeAlternativesParallel(scoredAlternatives, currentNode, goal);

          // Check if any path achieved the goal
          const completedNode = executedNodes.find(node => this._isGoalAchieved(node, goal));
          if (completedNode) {
            completedNode.state = 'complete';
            break;
          }
        } else {
          // Sequential execution (original behavior)
          const bestAlternative = this._selectBestAlternative(scoredAlternatives);

          if (bestAlternative) {
            const newNode = await this._executeAlternative(bestAlternative, currentNode);

            // Check for completion
            if (this._isGoalAchieved(newNode, goal)) {
              newNode.state = 'complete';
              break;
            }
          } else {
            console.log('No suitable alternatives found, ending reasoning');
            break;
          }
        }
      }

      const result = this._extractFinalResult();
      
      // Store reasoning pattern in memory for future learning
      await this._storeReasoningPattern(goal, this.currentTree);
      
      this.setStatus('idle');
      this.events.emit('reasoningCompleted', {
        agent: this.id,
        goal,
        result,
        tree: this.currentTree,
        timestamp: new Date()
      });

      return result;

    } catch (error) {
      this.setStatus('error');
      this.events.emit('reasoningError', {
        agent: this.id,
        goal,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Initialize a new reasoning tree with the root node
   * @param {string} goal - The goal to achieve
   * @private
   */
  _initializeReasoningTree(goal) {
    this.nodeCounter = 0;
    this.activeNodes.clear();
    
    const rootNode = new ThoughtNode({
      id: this._generateNodeId(),
      thought: `Initial analysis of the goal: "${goal}". I need to break this down into actionable steps.`,
      state: 'pending',
      depth: 0,
      metadata: { isRoot: true, goal }
    });
    
    this.activeNodes.set(rootNode.id, rootNode);
    this.currentTree = {
      rootId: rootNode.id,
      goal,
      startTime: new Date(),
      nodes: this.activeNodes
    };
  }

  /**
   * Generate a unique node ID
   * @returns {string} - Unique node identifier
   * @private
   */
  _generateNodeId() {
    return `node_${this.id}_${++this.nodeCounter}_${Date.now()}`;
  }

  /**
   * Select the next node to explore based on the exploration strategy
   * @returns {ThoughtNode|null} - The next node to explore
   * @private
   */
  _selectNextNode() {
    const pendingNodes = Array.from(this.activeNodes.values())
      .filter(node => node.state === 'pending');

    if (pendingNodes.length === 0) return null;

    // For parallel exploration, prefer nodes that haven't been explored yet
    if (this.parallelExploration) {
      const unexploredNodes = pendingNodes.filter(node =>
        !node.children || node.children.length === 0
      );

      if (unexploredNodes.length > 0) {
        return this._selectFromNodes(unexploredNodes);
      }
    }

    return this._selectFromNodes(pendingNodes);
  }

  /**
   * Select a node from a given set based on exploration strategy
   * @param {Array<ThoughtNode>} nodes - Nodes to select from
   * @returns {ThoughtNode} - Selected node
   * @private
   */
  _selectFromNodes(nodes) {
    switch (this.explorationStrategy) {
      case 'best-first':
        return nodes.reduce((best, current) =>
          current.score > best.score ? current : best
        );
      case 'depth-first':
        return nodes.reduce((deepest, current) =>
          current.depth > deepest.depth ? current : deepest
        );
      case 'breadth-first':
        return nodes.reduce((shallowest, current) =>
          current.depth < shallowest.depth ? current : shallowest
        );
      case 'balanced':
        // Balanced strategy: consider both score and depth
        return nodes.reduce((best, current) => {
          const bestScore = best.score + (1 / (best.depth + 1));
          const currentScore = current.score + (1 / (current.depth + 1));
          return currentScore > bestScore ? current : best;
        });
      default:
        return nodes[0];
    }
  }

  /**
   * Generate alternative thoughts/actions from the current node
   * @param {string} goal - The overall goal
   * @param {ThoughtNode} currentNode - The current node being explored
   * @param {Object} context - Additional context
   * @returns {Promise<Array>} - Array of alternative thoughts/actions
   * @private
   */
  async _generateAlternatives(goal, currentNode, context) {
    const path = currentNode.getPath(this.activeNodes);
    const pathSummary = path.map(node => ({
      thought: node.thought,
      action: node.action,
      result: node.result
    }));

    const prompt = this._buildAlternativesPrompt(goal, pathSummary, context);
    
    try {
      const response = await this.llmProvider.generateContent({
        contents: [{ role: "user", parts: [{ text: prompt }] }],
        config: {
          temperature: 0.8, // Higher temperature for more creative alternatives
          maxOutputTokens: 1024
        }
      });

      const responseText = response.candidates[0].content.parts[0].text;
      const alternatives = this._parseAlternatives(responseText);
      
      this.events.emit('thoughtGenerated', {
        agent: this.id,
        currentNode: currentNode.id,
        alternatives,
        timestamp: new Date()
      });

      return alternatives;

    } catch (error) {
      console.error('Error generating alternatives:', error);
      return [];
    }
  }

  /**
   * Build the prompt for generating alternative thoughts/actions
   * @param {string} goal - The overall goal
   * @param {Array} pathSummary - Summary of the current reasoning path
   * @param {Object} context - Additional context
   * @returns {string} - The formatted prompt
   * @private
   */
  _buildAlternativesPrompt(goal, pathSummary, context) {
    return `
You are an expert reasoning agent exploring multiple paths to achieve a goal.

GOAL: "${goal}"

CURRENT REASONING PATH:
${pathSummary.map((step, i) => `
Step ${i + 1}:
- Thought: ${step.thought}
- Action: ${step.action || 'None yet'}
- Result: ${step.result || 'Pending'}
`).join('')}

CONTEXT: ${JSON.stringify(context, null, 2)}

Generate ${this.maxBranches} alternative next steps. Each alternative should include:
1. A "thought" - your reasoning about what to do next
2. An "action" - a specific action to take (can be a tool call, analysis, or "FinalAnswer" if goal is achieved)

Consider different approaches: direct solutions, breaking down the problem, gathering more information, or alternative strategies.

Respond with a JSON array of objects, each with "thought" and "action" properties:
[
  {
    "thought": "Your reasoning here",
    "action": "Specific action to take"
  }
]
`;
  }

  /**
   * Parse alternatives from LLM response
   * @param {string} responseText - Raw response from LLM
   * @returns {Array} - Parsed alternatives
   * @private
   */
  _parseAlternatives(responseText) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback: try to parse the entire response
      return JSON.parse(responseText);
    } catch (error) {
      console.error('Error parsing alternatives:', error);
      console.log('Raw response:', responseText);

      // Fallback: create a single alternative from the response
      return [{
        thought: "Continue with current approach based on LLM response",
        action: responseText.substring(0, 200) + "..."
      }];
    }
  }

  /**
   * Score each alternative using the LLM
   * @param {string} goal - The overall goal
   * @param {Array} alternatives - Array of alternative thoughts/actions
   * @param {ThoughtNode} currentNode - The current node
   * @returns {Promise<Array>} - Array of scored alternatives
   * @private
   */
  async _scoreAlternatives(goal, alternatives, currentNode) {
    const scoredAlternatives = [];

    for (const alternative of alternatives) {
      try {
        const score = await this._scoreAlternative(goal, alternative, currentNode);
        scoredAlternatives.push({
          ...alternative,
          score,
          nodeId: this._generateNodeId()
        });

        this.events.emit('thoughtScored', {
          agent: this.id,
          alternative,
          score,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Error scoring alternative:', error);
        // Assign a default low score if scoring fails
        scoredAlternatives.push({
          ...alternative,
          score: 3.0,
          nodeId: this._generateNodeId()
        });
      }
    }

    return scoredAlternatives;
  }

  /**
   * Score a single alternative
   * @param {string} goal - The overall goal
   * @param {Object} alternative - The alternative to score
   * @param {ThoughtNode} currentNode - The current node
   * @returns {Promise<number>} - Score from 0-10
   * @private
   */
  async _scoreAlternative(goal, alternative, currentNode) {
    const path = currentNode.getPath(this.activeNodes);
    const pathSummary = path.map(node => `${node.thought} -> ${node.action || 'pending'}`).join('\n');

    const scoringPrompt = `
You are an expert evaluator assessing reasoning steps toward a goal.

GOAL: "${goal}"

CURRENT PATH:
${pathSummary}

PROPOSED NEXT STEP:
Thought: ${alternative.thought}
Action: ${alternative.action}

Rate this proposed step on a scale of 0-10 considering:
- Logical soundness (does it make sense?)
- Progress toward goal (does it move us closer?)
- Efficiency (is it a good use of effort?)
- Feasibility (can it actually be executed?)
- Risk level (what could go wrong?)

Respond with ONLY a number between 0 and 10 (decimals allowed).
`;

    try {
      const response = await this.llmProvider.generateContent({
        contents: [{ role: "user", parts: [{ text: scoringPrompt }] }],
        config: {
          temperature: 0.3, // Lower temperature for consistent scoring
          maxOutputTokens: 50
        }
      });

      const scoreText = response.candidates[0].content.parts[0].text.trim();
      const score = parseFloat(scoreText);

      // Validate score is in expected range
      if (isNaN(score) || score < 0 || score > 10) {
        console.warn(`Invalid score received: ${scoreText}, using default 5.0`);
        return 5.0;
      }

      return score;

    } catch (error) {
      console.error('Error in scoring:', error);
      return 5.0; // Default neutral score
    }
  }

  /**
   * Select the best alternative based on score and strategy
   * @param {Array} scoredAlternatives - Array of alternatives with scores
   * @returns {Object|null} - The best alternative or null
   * @private
   */
  _selectBestAlternative(scoredAlternatives) {
    if (scoredAlternatives.length === 0) return null;

    // Filter alternatives that meet the scoring threshold
    const viableAlternatives = scoredAlternatives.filter(alt => alt.score >= this.scoringThreshold);

    if (viableAlternatives.length === 0) {
      console.log(`No alternatives meet threshold ${this.scoringThreshold}, using best available`);
      return scoredAlternatives.reduce((best, current) =>
        current.score > best.score ? current : best
      );
    }

    // Select the highest scoring viable alternative
    const bestAlternative = viableAlternatives.reduce((best, current) =>
      current.score > best.score ? current : best
    );

    this.events.emit('pathSelected', {
      agent: this.id,
      selectedAlternative: bestAlternative,
      allAlternatives: scoredAlternatives,
      timestamp: new Date()
    });

    return bestAlternative;
  }

  /**
   * Execute multiple alternatives in parallel
   * @param {Array} scoredAlternatives - Array of alternatives with scores
   * @param {ThoughtNode} parentNode - The parent node
   * @param {string} goal - The overall goal
   * @returns {Promise<Array<ThoughtNode>>} - Array of executed nodes
   * @private
   */
  async _executeAlternativesParallel(scoredAlternatives, parentNode, goal) {
    // Select top alternatives for parallel execution
    const topAlternatives = scoredAlternatives
      .sort((a, b) => b.score - a.score)
      .slice(0, this.maxParallelPaths)
      .filter(alt => alt.score >= this.scoringThreshold);

    if (topAlternatives.length === 0) {
      console.log('No alternatives meet threshold for parallel execution');
      return [];
    }

    console.log(`🔀 Executing ${topAlternatives.length} alternatives in parallel`);

    // Execute alternatives concurrently
    const executionPromises = topAlternatives.map(async (alternative) => {
      try {
        return await this._executeAlternative(alternative, parentNode);
      } catch (error) {
        console.error(`Error in parallel execution of alternative: ${error.message}`);
        // Create a failed node
        const failedNode = new ThoughtNode({
          id: alternative.nodeId,
          parentId: parentNode.id,
          thought: alternative.thought,
          action: alternative.action,
          score: alternative.score,
          state: 'failed',
          depth: parentNode.depth + 1,
          result: { error: error.message }
        });
        parentNode.addChild(failedNode);
        this.activeNodes.set(failedNode.id, failedNode);
        return failedNode;
      }
    });

    const executedNodes = await Promise.all(executionPromises);

    // Emit parallel execution event
    this.events.emit('parallelExecution', {
      agent: this.id,
      parentNode: parentNode.id,
      executedNodes: executedNodes.map(n => n.id),
      successCount: executedNodes.filter(n => n.state === 'complete').length,
      timestamp: new Date()
    });

    return executedNodes;
  }

  /**
   * Execute the selected alternative and create a new node
   * @param {Object} alternative - The alternative to execute
   * @param {ThoughtNode} parentNode - The parent node
   * @returns {Promise<ThoughtNode>} - The new node created from execution
   * @private
   */
  async _executeAlternative(alternative, parentNode) {
    const newNode = new ThoughtNode({
      id: alternative.nodeId,
      parentId: parentNode.id,
      thought: alternative.thought,
      action: alternative.action,
      score: alternative.score,
      state: 'executing',
      depth: parentNode.depth + 1
    });

    // Add to parent and active nodes
    parentNode.addChild(newNode);
    this.activeNodes.set(newNode.id, newNode);

    try {
      // Execute the action
      const result = await this._executeAction(alternative.action, newNode);

      newNode.result = result;
      newNode.state = 'complete';

      console.log(`Executed action: ${alternative.action}`);
      console.log(`Result: ${JSON.stringify(result, null, 2)}`);

      return newNode;

    } catch (error) {
      console.error(`Error executing action: ${error.message}`);
      newNode.result = { error: error.message };
      newNode.state = 'failed';
      return newNode;
    }
  }

  /**
   * Execute a specific action (tool call, analysis, etc.)
   * @param {string} action - The action to execute
   * @param {ThoughtNode} node - The node context
   * @returns {Promise<any>} - The result of the action
   * @private
   */
  async _executeAction(action, node) {
    // Check if this is a final answer
    if (action.toLowerCase().includes('finalanswer') || action.toLowerCase().includes('final answer')) {
      return {
        type: 'final_answer',
        content: action,
        timestamp: new Date()
      };
    }

    // Check if this is a tool call
    if (action.startsWith('tool:') || this._isToolCall(action)) {
      return await this._executeToolAction(action, node);
    }

    // Check if this is an analysis or reasoning step
    if (action.toLowerCase().includes('analyze') || action.toLowerCase().includes('think')) {
      return await this._executeAnalysisAction(action, node);
    }

    // Default: treat as a planning or information gathering step
    return {
      type: 'planning_step',
      content: action,
      timestamp: new Date(),
      status: 'completed'
    };
  }

  /**
   * Check if an action is a tool call
   * @param {string} action - The action to check
   * @returns {boolean} - True if it's a tool call
   * @private
   */
  _isToolCall(action) {
    // Check if action mentions any available tools
    const toolNames = Object.keys(this.tools);
    return toolNames.some(toolName =>
      action.toLowerCase().includes(toolName.toLowerCase())
    );
  }

  /**
   * Execute a tool-based action
   * @param {string} action - The tool action to execute
   * @param {ThoughtNode} node - The node context
   * @returns {Promise<any>} - The tool execution result
   * @private
   */
  async _executeToolAction(action, node) {
    try {
      // Parse tool name and parameters from action
      const { toolName, params } = this._parseToolAction(action);

      if (!this.tools[toolName]) {
        throw new Error(`Tool '${toolName}' not found`);
      }

      // Execute the tool
      const result = await this.toolHandler.executeTool([{
        function: {
          name: toolName,
          args: params
        }
      }], this.tools);

      return {
        type: 'tool_execution',
        toolName,
        params,
        result,
        timestamp: new Date()
      };

    } catch (error) {
      return {
        type: 'tool_execution',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Parse tool action to extract tool name and parameters
   * @param {string} action - The action string
   * @returns {Object} - Object with toolName and params
   * @private
   */
  _parseToolAction(action) {
    // Simple parsing - can be enhanced for more complex tool calls
    const toolNames = Object.keys(this.tools);
    const foundTool = toolNames.find(name =>
      action.toLowerCase().includes(name.toLowerCase())
    );

    if (foundTool) {
      return {
        toolName: foundTool,
        params: {} // Default empty params - could be enhanced to parse from action
      };
    }

    // Fallback for tool: prefix
    if (action.startsWith('tool:')) {
      const parts = action.substring(5).split('(');
      return {
        toolName: parts[0].trim(),
        params: parts[1] ? this._parseParams(parts[1]) : {}
      };
    }

    throw new Error(`Could not parse tool action: ${action}`);
  }

  /**
   * Parse parameters from a string
   * @param {string} paramString - The parameter string
   * @returns {Object} - Parsed parameters
   * @private
   */
  _parseParams(paramString) {
    try {
      // Remove closing parenthesis and try to parse as JSON
      const cleanString = paramString.replace(/\)$/, '');
      return JSON.parse(`{${cleanString}}`);
    } catch (error) {
      // Fallback: return as single parameter
      return { input: paramString.replace(/\)$/, '') };
    }
  }

  /**
   * Execute an analysis or reasoning action
   * @param {string} action - The analysis action
   * @param {ThoughtNode} node - The node context
   * @returns {Promise<any>} - The analysis result
   * @private
   */
  async _executeAnalysisAction(action, node) {
    const analysisPrompt = `
You are performing a reasoning step as part of a larger goal-solving process.

CURRENT CONTEXT:
- Goal: ${this.currentTree.goal}
- Current thought: ${node.thought}
- Action to perform: ${action}

Please perform this analysis step and provide a clear, actionable result.
Focus on moving closer to the overall goal.
`;

    try {
      const response = await this.llmProvider.generateContent({
        contents: [{ role: "user", parts: [{ text: analysisPrompt }] }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 512
        }
      });

      const analysisResult = response.candidates[0].content.parts[0].text;

      return {
        type: 'analysis',
        action,
        result: analysisResult,
        timestamp: new Date()
      };

    } catch (error) {
      return {
        type: 'analysis',
        action,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check if the goal has been achieved
   * @param {ThoughtNode} node - The current node to check
   * @param {string} goal - The original goal
   * @returns {boolean} - True if goal is achieved
   * @private
   */
  _isGoalAchieved(node, goal) {
    // Check if the action indicates completion
    if (node.action && (
      node.action.toLowerCase().includes('finalanswer') ||
      node.action.toLowerCase().includes('final answer') ||
      node.action.toLowerCase().includes('complete')
    )) {
      return true;
    }

    // Check if the result indicates completion
    if (node.result && node.result.type === 'final_answer') {
      return true;
    }

    // Check if the thought indicates completion
    if (node.thought && (
      node.thought.toLowerCase().includes('goal achieved') ||
      node.thought.toLowerCase().includes('task complete') ||
      node.thought.toLowerCase().includes('finished')
    )) {
      return true;
    }

    return false;
  }

  /**
   * Extract the final result from the reasoning tree
   * @returns {Object} - The final result with tree metadata
   * @private
   */
  _extractFinalResult() {
    const completedNodes = Array.from(this.activeNodes.values())
      .filter(node => node.state === 'complete');

    const finalNode = completedNodes
      .sort((a, b) => b.depth - a.depth)[0]; // Get the deepest completed node

    if (!finalNode) {
      return {
        success: false,
        message: 'No completed reasoning path found',
        tree: this._serializeTree(),
        timestamp: new Date()
      };
    }

    const path = finalNode.getPath(this.activeNodes);

    return {
      success: true,
      finalNode: finalNode.toJSON(),
      reasoningPath: path.map(node => node.toJSON()),
      tree: this._serializeTree(),
      summary: this._generatePathSummary(path),
      timestamp: new Date()
    };
  }

  /**
   * Generate a human-readable summary of the reasoning path
   * @param {ThoughtNode[]} path - The reasoning path
   * @returns {string} - Summary of the path
   * @private
   */
  _generatePathSummary(path) {
    return path.map((node, index) => {
      let summary = `Step ${index + 1}: ${node.thought}`;
      if (node.action) {
        summary += `\n  Action: ${node.action}`;
      }
      if (node.result && typeof node.result === 'object') {
        summary += `\n  Result: ${JSON.stringify(node.result, null, 2)}`;
      } else if (node.result) {
        summary += `\n  Result: ${node.result}`;
      }
      return summary;
    }).join('\n\n');
  }

  /**
   * Serialize the entire reasoning tree for storage/analysis
   * @returns {Object} - Serialized tree structure
   * @private
   */
  _serializeTree() {
    const serializedNodes = {};
    for (const [id, node] of this.activeNodes) {
      serializedNodes[id] = node.toJSON();
    }

    return {
      rootId: this.currentTree.rootId,
      goal: this.currentTree.goal,
      startTime: this.currentTree.startTime,
      endTime: new Date(),
      nodes: serializedNodes,
      statistics: this._calculateTreeStatistics()
    };
  }

  /**
   * Calculate statistics about the reasoning tree
   * @returns {Object} - Tree statistics
   * @private
   */
  _calculateTreeStatistics() {
    const nodes = Array.from(this.activeNodes.values());
    const completedNodes = nodes.filter(n => n.state === 'complete');
    const failedNodes = nodes.filter(n => n.state === 'failed');

    return {
      totalNodes: nodes.length,
      completedNodes: completedNodes.length,
      failedNodes: failedNodes.length,
      maxDepth: Math.max(...nodes.map(n => n.depth)),
      averageScore: nodes.reduce((sum, n) => sum + n.score, 0) / nodes.length,
      explorationEfficiency: completedNodes.length / nodes.length
    };
  }

  /**
   * Store the reasoning pattern in memory for future learning
   * @param {string} goal - The original goal
   * @param {Object} tree - The reasoning tree
   * @returns {Promise<void>}
   * @private
   */
  async _storeReasoningPattern(goal, tree) {
    try {
      const existingPatterns = this.memory.recall(this.reasoningMemoryKey) || [];

      const pattern = {
        goal,
        tree: this._serializeTree(),
        timestamp: new Date(),
        success: tree.nodes && Object.values(tree.nodes).some(n => n.state === 'complete')
      };

      existingPatterns.push(pattern);

      // Keep only the last 50 patterns to avoid memory bloat
      if (existingPatterns.length > 50) {
        existingPatterns.splice(0, existingPatterns.length - 50);
      }

      await this.memory.rememberAsync(this.reasoningMemoryKey, existingPatterns);

      console.log(`Stored reasoning pattern for goal: ${goal}`);

    } catch (error) {
      console.error('Error storing reasoning pattern:', error);
    }
  }

  /**
   * Get similar reasoning patterns from memory
   * @param {string} goal - The current goal
   * @returns {Array} - Array of similar reasoning patterns
   */
  getSimilarReasoningPatterns(goal) {
    const patterns = this.memory.recall(this.reasoningMemoryKey) || [];

    // Simple similarity matching - could be enhanced with embeddings
    return patterns.filter(pattern =>
      pattern.goal.toLowerCase().includes(goal.toLowerCase()) ||
      goal.toLowerCase().includes(pattern.goal.toLowerCase())
    ).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Event handlers for tree operations
   */
  _onThoughtGenerated(data) {
    console.log(`💭 Generated ${data.alternatives.length} alternatives for node ${data.currentNode}`);
  }

  _onThoughtScored(data) {
    console.log(`📊 Scored alternative: ${data.alternative.thought.substring(0, 50)}... Score: ${data.score}`);
  }

  _onPathSelected(data) {
    console.log(`🎯 Selected path: ${data.selectedAlternative.thought.substring(0, 50)}... (Score: ${data.selectedAlternative.score})`);
  }

  /**
   * Get the current reasoning tree (for debugging/visualization)
   * @returns {Object|null} - The current tree or null if no active reasoning
   */
  getCurrentTree() {
    return this.currentTree ? this._serializeTree() : null;
  }

  /**
   * Visualize the reasoning tree as a simple text representation
   * @returns {string} - Text representation of the tree
   */
  visualizeTree() {
    if (!this.currentTree) return 'No active reasoning tree';

    const rootNode = this.activeNodes.get(this.currentTree.rootId);
    return this._visualizeNode(rootNode, 0);
  }

  /**
   * Recursively visualize a node and its children
   * @param {ThoughtNode} node - The node to visualize
   * @param {number} depth - Current depth for indentation
   * @returns {string} - Text representation of the node and children
   * @private
   */
  _visualizeNode(node, depth) {
    const indent = '  '.repeat(depth);
    const status = node.state === 'complete' ? '✅' :
                  node.state === 'failed' ? '❌' :
                  node.state === 'executing' ? '⏳' : '⏸️';

    let result = `${indent}${status} [${node.score.toFixed(1)}] ${node.thought}\n`;

    if (node.action) {
      result += `${indent}    Action: ${node.action}\n`;
    }

    for (const child of node.children) {
      result += this._visualizeNode(child, depth + 1);
    }

    return result;
  }
}
