import { ReflectiveAgent } from './ReflectiveAgent.js';

/**
 * Example tools for the ReflectiveAgent
 * These demonstrate how existing tools integrate with tree-of-thought reasoning
 */

// Mock LLM Provider for testing
class MockLL<PERSON>rovider {
  async generateContent(request) {
    // Simulate different types of responses based on the prompt
    const prompt = request.contents[0].parts[0].text.toLowerCase();
    
    if (prompt.includes('generate') && prompt.includes('alternative')) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify([
                {
                  "thought": "Break down the problem into smaller, manageable components",
                  "action": "analyze_requirements"
                },
                {
                  "thought": "Research existing solutions and best practices",
                  "action": "tool:research(topic: 'best practices')"
                },
                {
                  "thought": "Create a step-by-step implementation plan",
                  "action": "create_plan"
                }
              ])
            }]
          }
        }]
      };
    }
    
    if (prompt.includes('rate') || prompt.includes('score')) {
      // Return a random score between 6-9 for testing
      const score = (Math.random() * 3 + 6).toFixed(1);
      return {
        candidates: [{
          content: {
            parts: [{ text: score }]
          }
        }]
      };
    }
    
    if (prompt.includes('analysis') || prompt.includes('perform')) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: "Analysis complete. The requirements have been broken down into three main categories: functional requirements, technical constraints, and user experience considerations. Each category has been prioritized based on business impact and implementation complexity."
            }]
          }
        }]
      };
    }
    
    // Default response
    return {
      candidates: [{
        content: {
          parts: [{
            text: "I understand the request and will proceed with the next logical step."
          }]
        }]
      }]
    };
  }
}

// Example tools that work with ReflectiveAgent
const exampleTools = {
  research: {
    name: 'research',
    schema: {
      function_declaration: {
        name: 'research',
        description: 'Research information on a given topic',
        parameters: {
          type: 'object',
          properties: {
            topic: {
              type: 'string',
              description: 'The topic to research'
            },
            depth: {
              type: 'string',
              enum: ['basic', 'detailed', 'comprehensive'],
              description: 'The depth of research required'
            }
          },
          required: ['topic']
        }
      }
    },
    call: async (params) => {
      return {
        topic: params.topic,
        findings: [
          `Key insight 1 about ${params.topic}`,
          `Key insight 2 about ${params.topic}`,
          `Best practice for ${params.topic}`
        ],
        sources: ['source1.com', 'source2.com'],
        timestamp: new Date()
      };
    }
  },

  plan_creator: {
    name: 'plan_creator',
    schema: {
      function_declaration: {
        name: 'plan_creator',
        description: 'Create a structured plan for achieving a goal',
        parameters: {
          type: 'object',
          properties: {
            goal: {
              type: 'string',
              description: 'The goal to create a plan for'
            },
            timeframe: {
              type: 'string',
              description: 'The timeframe for the plan'
            }
          },
          required: ['goal']
        }
      }
    },
    call: async (params) => {
      return {
        goal: params.goal,
        steps: [
          'Step 1: Initial analysis and requirements gathering',
          'Step 2: Design and architecture planning',
          'Step 3: Implementation and development',
          'Step 4: Testing and validation',
          'Step 5: Deployment and monitoring'
        ],
        timeframe: params.timeframe || 'Not specified',
        estimatedEffort: 'Medium',
        timestamp: new Date()
      };
    }
  },

  validator: {
    name: 'validator',
    schema: {
      function_declaration: {
        name: 'validator',
        description: 'Validate a solution or approach',
        parameters: {
          type: 'object',
          properties: {
            solution: {
              type: 'string',
              description: 'The solution to validate'
            },
            criteria: {
              type: 'array',
              items: { type: 'string' },
              description: 'Validation criteria'
            }
          },
          required: ['solution']
        }
      }
    },
    call: async (params) => {
      return {
        solution: params.solution,
        isValid: true,
        score: 8.5,
        feedback: [
          'Solution addresses the core requirements',
          'Implementation approach is sound',
          'Consider adding error handling'
        ],
        timestamp: new Date()
      };
    }
  }
};

/**
 * Create and configure a ReflectiveAgent with example tools
 */
function createExampleReflectiveAgent() {
  const agent = new ReflectiveAgent({
    id: 'reflective-agent-1',
    name: 'Example Reflective Agent',
    description: 'A demonstration agent using tree-of-thought reasoning',
    role: 'You are an expert problem-solving agent that uses systematic reasoning to achieve goals.',
    llmProvider: new MockLLMProvider(),
    maxDepth: 4,
    maxBranches: 3,
    explorationStrategy: 'best-first',
    scoringThreshold: 6.0,
    goals: [
      'Break down complex problems systematically',
      'Explore multiple solution paths',
      'Validate solutions before implementation'
    ]
  });

  // Add the example tools
  Object.values(exampleTools).forEach(tool => {
    agent.addTool(tool);
  });

  return agent;
}

/**
 * Example usage scenarios
 */
async function runExampleScenarios() {
  console.log('🚀 Creating ReflectiveAgent with tree-of-thought capabilities...\n');
  
  const agent = createExampleReflectiveAgent();
  
  // Scenario 1: Software Development Planning
  console.log('📋 Scenario 1: Software Development Planning');
  console.log('=' .repeat(50));
  
  try {
    const result1 = await agent.executeReflectivePlan(
      "Design and implement a user authentication system for a web application",
      {
        context: {
          technology: 'Node.js',
          database: 'PostgreSQL',
          requirements: ['secure', 'scalable', 'user-friendly']
        },
        maxDepth: 3
      }
    );
    
    console.log('\n✅ Planning Result:');
    console.log(JSON.stringify(result1, null, 2));
    
    console.log('\n🌳 Reasoning Tree Visualization:');
    console.log(agent.visualizeTree());
    
  } catch (error) {
    console.error('❌ Error in scenario 1:', error);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Scenario 2: Business Problem Solving
  console.log('💼 Scenario 2: Business Problem Solving');
  console.log('=' .repeat(50));
  
  try {
    const result2 = await agent.executeReflectivePlan(
      "Increase customer retention rate by 25% within 6 months",
      {
        context: {
          currentRetention: '70%',
          industry: 'SaaS',
          budget: '$50,000'
        },
        maxDepth: 4
      }
    );
    
    console.log('\n✅ Business Solution Result:');
    console.log(JSON.stringify(result2, null, 2));
    
    console.log('\n🌳 Reasoning Tree Visualization:');
    console.log(agent.visualizeTree());
    
  } catch (error) {
    console.error('❌ Error in scenario 2:', error);
  }
  
  // Show reasoning patterns learned
  console.log('\n🧠 Learned Reasoning Patterns:');
  const patterns = agent.getSimilarReasoningPatterns('design');
  console.log(`Found ${patterns.length} similar patterns in memory`);
  
  return agent;
}

/**
 * Demonstrate integration with existing Agency.js workflow
 */
async function demonstrateAgencyIntegration() {
  console.log('\n🔗 Agency Integration Example');
  console.log('=' .repeat(50));
  
  const agent = createExampleReflectiveAgent();
  
  // Simulate how this would work with Agency.js planJobsFromGoal
  const goal = "Create a comprehensive marketing strategy";
  
  console.log(`Goal: ${goal}`);
  console.log('\n🤔 Using ReflectiveAgent for enhanced planning...');
  
  try {
    const reflectiveResult = await agent.executeReflectivePlan(goal, {
      context: { 
        budget: '$100,000',
        timeline: '3 months',
        target: 'B2B SaaS companies'
      }
    });
    
    console.log('\n📊 Enhanced Planning Result:');
    console.log('Success:', reflectiveResult.success);
    console.log('Reasoning Steps:', reflectiveResult.reasoningPath.length);
    console.log('Tree Statistics:', reflectiveResult.tree.statistics);
    
    // Show how this could be converted to traditional Agency jobs
    console.log('\n🔄 Converting to Agency.js Jobs:');
    const jobs = convertReflectiveResultToJobs(reflectiveResult, goal);
    jobs.forEach((job, index) => {
      console.log(`Job ${index + 1}: ${job.name}`);
      console.log(`  Description: ${job.description}`);
      console.log(`  Inputs: ${job.inputs.join(', ')}`);
      console.log(`  Outputs: ${job.outputs.join(', ')}\n`);
    });
    
  } catch (error) {
    console.error('❌ Error in agency integration:', error);
  }
}

/**
 * Convert ReflectiveAgent result to traditional Agency.js job format
 */
function convertReflectiveResultToJobs(reflectiveResult, goal) {
  if (!reflectiveResult.success || !reflectiveResult.reasoningPath) {
    return [];
  }
  
  return reflectiveResult.reasoningPath
    .filter(step => step.action && !step.action.includes('FinalAnswer'))
    .map((step, index) => ({
      name: `Step ${index + 1}: ${step.thought.substring(0, 50)}...`,
      description: step.thought,
      inputs: ['previous_step_output', 'context'],
      outputs: [`step_${index + 1}_result`],
      action: step.action,
      score: step.score
    }));
}

// Export for use in other modules
export {
  ReflectiveAgent,
  createExampleReflectiveAgent,
  runExampleScenarios,
  demonstrateAgencyIntegration,
  exampleTools
};

// Run examples if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🎯 Running ReflectiveAgent Examples...\n');
  
  runExampleScenarios()
    .then(() => demonstrateAgencyIntegration())
    .then(() => console.log('\n✨ All examples completed!'))
    .catch(console.error);
}
