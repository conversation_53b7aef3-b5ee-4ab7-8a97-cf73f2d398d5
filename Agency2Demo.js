import { EnhancedAgency } from './EnhancedAgency.js';
import { TreeVisualizer, ReflectiveDebugger } from './ReflectiveVisualization.js';
import { AdvancedMockLLMProvider } from './ReflectiveAgentTest.js';
import fs from 'fs';
import path from 'path';

/**
 * Comprehensive demonstration of Agency2.js - Tree-of-Thought Implementation
 * Shows the complete integration of ReflectiveAgent with the existing Agency framework
 */

/**
 * Create a realistic LLM provider for demonstration
 */
class DemoLL<PERSON>rovider extends AdvancedMockLLMProvider {
  constructor() {
    super();
    this.scenarioResponses = new Map();
    this._initializeScenarioResponses();
  }

  _initializeScenarioResponses() {
    // Software development scenarios
    this.scenarioResponses.set('software_auth', {
      alternatives: [
        {
          "thought": "Analyze security requirements and compliance standards (GDPR, CCPA)",
          "action": "tool:security_analyzer(scope: 'authentication requirements')"
        },
        {
          "thought": "Design database schema for user management and session handling",
          "action": "create_database_design"
        },
        {
          "thought": "Implement OAuth 2.0 with JWT tokens for scalable authentication",
          "action": "tool:oauth_implementer(type: 'JWT', provider: 'custom')"
        }
      ],
      scores: [8.5, 7.2, 9.1],
      analysis: "Security analysis reveals need for multi-factor authentication, session management, and secure password storage. Recommend bcrypt for hashing and Redis for session storage."
    });

    // Business strategy scenarios
    this.scenarioResponses.set('business_retention', {
      alternatives: [
        {
          "thought": "Conduct customer churn analysis to identify at-risk segments",
          "action": "tool:analytics(type: 'churn_analysis', timeframe: '12_months')"
        },
        {
          "thought": "Implement personalized customer success program with dedicated CSMs",
          "action": "design_customer_success_program"
        },
        {
          "thought": "Create loyalty rewards program with tiered benefits",
          "action": "tool:loyalty_designer(tiers: 3, benefits: 'progressive')"
        }
      ],
      scores: [8.8, 7.5, 6.9],
      analysis: "Customer data shows highest churn in months 3-6. Implementing proactive outreach and value demonstration programs should improve retention by 15-20%."
    });
  }

  async generateContent(request) {
    const prompt = request.contents[0].parts[0].text.toLowerCase();
    
    // Check for specific scenarios
    if (prompt.includes('authentication') && prompt.includes('alternative')) {
      const scenario = this.scenarioResponses.get('software_auth');
      return {
        candidates: [{
          content: {
            parts: [{ text: JSON.stringify(scenario.alternatives) }]
          }
        }]
      };
    }
    
    if (prompt.includes('retention') && prompt.includes('alternative')) {
      const scenario = this.scenarioResponses.get('business_retention');
      return {
        candidates: [{
          content: {
            parts: [{ text: JSON.stringify(scenario.alternatives) }]
          }
        }]
      };
    }
    
    // Use parent class for other responses
    return super.generateContent(request);
  }
}

/**
 * Create comprehensive tool suite for demonstration
 */
function createDemoTools() {
  return {
    security_analyzer: {
      name: 'security_analyzer',
      schema: {
        function_declaration: {
          name: 'security_analyzer',
          description: 'Analyze security requirements and vulnerabilities',
          parameters: {
            type: 'object',
            properties: {
              scope: { type: 'string', description: 'Analysis scope' },
              depth: { type: 'string', enum: ['basic', 'comprehensive'], default: 'comprehensive' }
            },
            required: ['scope']
          }
        }
      },
      call: async (params) => ({
        scope: params.scope,
        findings: [
          'OWASP Top 10 compliance required',
          'Multi-factor authentication recommended',
          'Session timeout: 30 minutes maximum',
          'Password policy: 12+ characters, complexity required'
        ],
        riskLevel: 'Medium',
        recommendations: [
          'Implement rate limiting for login attempts',
          'Use secure session storage (Redis/encrypted cookies)',
          'Add audit logging for authentication events'
        ],
        timestamp: new Date()
      })
    },

    oauth_implementer: {
      name: 'oauth_implementer',
      schema: {
        function_declaration: {
          name: 'oauth_implementer',
          description: 'Generate OAuth 2.0 implementation plan',
          parameters: {
            type: 'object',
            properties: {
              type: { type: 'string', enum: ['JWT', 'opaque'] },
              provider: { type: 'string' }
            },
            required: ['type', 'provider']
          }
        }
      },
      call: async (params) => ({
        implementation: {
          tokenType: params.type,
          provider: params.provider,
          endpoints: ['/auth/login', '/auth/refresh', '/auth/logout'],
          security: 'RS256 signing, 15min access tokens, 7day refresh tokens'
        },
        codeSnippets: [
          'JWT generation with RS256',
          'Token validation middleware',
          'Refresh token rotation'
        ],
        estimatedEffort: '2-3 weeks',
        timestamp: new Date()
      })
    },

    analytics: {
      name: 'analytics',
      schema: {
        function_declaration: {
          name: 'analytics',
          description: 'Perform data analytics and generate insights',
          parameters: {
            type: 'object',
            properties: {
              type: { type: 'string', description: 'Type of analysis' },
              timeframe: { type: 'string', description: 'Analysis timeframe' }
            },
            required: ['type']
          }
        }
      },
      call: async (params) => ({
        analysisType: params.type,
        timeframe: params.timeframe || '6_months',
        insights: [
          'Customer churn rate: 23% annually',
          'Highest churn in months 3-6 after signup',
          'Support ticket volume correlates with churn risk',
          'Feature adoption rate impacts retention significantly'
        ],
        recommendations: [
          'Implement proactive customer success outreach',
          'Create feature adoption tracking dashboard',
          'Develop early warning system for at-risk accounts'
        ],
        confidence: 0.87,
        timestamp: new Date()
      })
    },

    loyalty_designer: {
      name: 'loyalty_designer',
      schema: {
        function_declaration: {
          name: 'loyalty_designer',
          description: 'Design customer loyalty program',
          parameters: {
            type: 'object',
            properties: {
              tiers: { type: 'number', description: 'Number of loyalty tiers' },
              benefits: { type: 'string', description: 'Benefit structure' }
            },
            required: ['tiers']
          }
        }
      },
      call: async (params) => ({
        program: {
          tiers: params.tiers,
          structure: params.benefits || 'progressive',
          tierNames: ['Bronze', 'Silver', 'Gold'].slice(0, params.tiers),
          benefits: [
            'Tier 1: 5% discount, priority support',
            'Tier 2: 10% discount, early access features',
            'Tier 3: 15% discount, dedicated account manager'
          ].slice(0, params.tiers)
        },
        expectedImpact: '12-18% improvement in retention',
        implementationTime: '6-8 weeks',
        timestamp: new Date()
      })
    }
  };
}

/**
 * Main demonstration function
 */
async function runAgency2Demo() {
  console.log('🚀 Agency2.js - Tree-of-Thought Demonstration');
  console.log('=' .repeat(60));
  console.log('This demo showcases the enhanced planning capabilities with reflective reasoning\n');

  // Create enhanced agency
  const agency = new EnhancedAgency({
    reflectivePlanningEnabled: true,
    maxDepth: 5,
    maxBranches: 3,
    explorationStrategy: 'best-first',
    scoringThreshold: 7.0
  });

  // Create demo agent
  const agentConfig = {
    id: 'demo-agent',
    name: 'Agency2 Demo Agent',
    description: 'Demonstration agent showcasing tree-of-thought capabilities',
    role: 'You are an expert problem-solving agent that uses systematic reasoning and available tools to achieve complex goals.',
    llmProvider: new DemoLLMProvider(),
    goals: [
      'Provide comprehensive solutions through systematic analysis',
      'Utilize available tools effectively',
      'Learn from each reasoning session to improve future performance'
    ]
  };

  agency.createAgent(agentConfig);
  
  // Add tools
  const tools = createDemoTools();
  const agent = agency.agents['demo-agent'];
  Object.values(tools).forEach(tool => agent.addTool(tool));

  console.log(`✅ Created agent "${agent.name}" with ${Object.keys(tools).length} tools\n`);

  // Demo scenarios
  const scenarios = [
    {
      name: 'Software Development: Authentication System',
      goal: 'Design and implement a secure, scalable user authentication system for a SaaS application',
      context: {
        userBase: '10,000+ users',
        compliance: 'SOC2, GDPR',
        technology: 'Node.js, PostgreSQL, Redis',
        timeline: '6 weeks'
      }
    },
    {
      name: 'Business Strategy: Customer Retention',
      goal: 'Develop a comprehensive strategy to improve customer retention by 25% within 12 months',
      context: {
        currentRetention: '77%',
        industry: 'B2B SaaS',
        customerBase: '2,500 companies',
        budget: '$500,000'
      }
    }
  ];

  const results = [];

  for (const [index, scenario] of scenarios.entries()) {
    console.log(`\n📋 Scenario ${index + 1}: ${scenario.name}`);
    console.log('=' .repeat(50));
    console.log(`🎯 Goal: ${scenario.goal}`);
    console.log(`📊 Context: ${JSON.stringify(scenario.context, null, 2)}\n`);

    try {
      // Use enhanced planning
      const planningResult = await agency.planJobsFromGoalEnhanced('demo-agent', scenario.goal, {
        context: scenario.context,
        forceReflective: true,
        maxDepth: 4
      });

      console.log('✅ Planning completed successfully!');
      console.log(`📈 Result type: ${planningResult.type}`);
      console.log(`🔢 Jobs created: ${planningResult.jobs ? planningResult.jobs.length : 0}`);
      
      if (planningResult.tree) {
        const stats = planningResult.tree.statistics;
        console.log(`🌳 Tree statistics:`);
        console.log(`   - Nodes: ${stats.totalNodes}`);
        console.log(`   - Max depth: ${stats.maxDepth}`);
        console.log(`   - Efficiency: ${(stats.explorationEfficiency * 100).toFixed(1)}%`);
        console.log(`   - Avg score: ${stats.averageScore.toFixed(1)}`);
      }

      // Generate visualizations
      const visualizer = new TreeVisualizer({
        showScores: true,
        showActions: true,
        showTimestamps: false
      });

      console.log('\n🌲 Tree Visualization:');
      console.log(visualizer.generateTextTree(planningResult.tree));

      // Generate analysis report
      const analysis = visualizer.generateAnalysisReport(planningResult.tree);
      console.log('\n📊 Analysis Report:');
      console.log(`   - Path efficiency: ${(analysis.pathAnalysis.shortestCompletePathLength / analysis.pathAnalysis.longestPathLength * 100).toFixed(1)}%`);
      console.log(`   - Score distribution: High (8-10): ${analysis.scoreAnalysis.scoreDistribution['8-10']}, Medium (6-8): ${analysis.scoreAnalysis.scoreDistribution['6-8']}`);
      console.log(`   - Recommendations: ${analysis.recommendations.length} suggestions`);
      
      if (analysis.recommendations.length > 0) {
        console.log('   - Top recommendation:', analysis.recommendations[0]);
      }

      // Save HTML visualization
      const htmlViz = visualizer.generateInteractiveHTML(planningResult.tree);
      const filename = `agency2_demo_scenario_${index + 1}.html`;
      fs.writeFileSync(filename, htmlViz);
      console.log(`💾 Interactive visualization saved to: ${filename}`);

      results.push({
        scenario: scenario.name,
        result: planningResult,
        analysis,
        filename
      });

    } catch (error) {
      console.error(`❌ Error in scenario ${index + 1}:`, error.message);
      results.push({
        scenario: scenario.name,
        error: error.message
      });
    }
  }

  // Compare with traditional planning
  console.log('\n⚖️  Comparing Traditional vs Reflective Planning');
  console.log('=' .repeat(60));

  for (const scenario of scenarios.slice(0, 1)) { // Just test first scenario for comparison
    try {
      const comparison = await agency.comparePlanningApproaches('demo-agent', scenario.goal);
      
      console.log(`\n🔍 Comparison for: ${scenario.goal.substring(0, 50)}...`);
      console.log(`📋 Traditional: ${comparison.traditional.jobCount} jobs, ${comparison.traditional.planningTime}ms`);
      console.log(`🧠 Reflective: ${comparison.reflective.jobCount} jobs, ${comparison.reflective.planningTime}ms`);
      console.log(`⚡ Speed ratio: ${comparison.comparison.timeRatio.toFixed(2)}x`);
      console.log(`📊 Quality advantage: ${comparison.comparison.reflectiveAdvantage ? 'Yes' : 'No'}`);
      
    } catch (error) {
      console.error('❌ Comparison failed:', error.message);
    }
  }

  // Show learning and memory
  console.log('\n🧠 Memory and Learning Analysis');
  console.log('=' .repeat(60));

  const stats = agency.getReflectivePlanningStats('demo-agent');
  console.log(`📈 Planning Statistics:`);
  console.log(`   - Total reflective plans: ${stats.totalReflectivePlans}`);
  console.log(`   - Success rate: ${(stats.successRate * 100).toFixed(1)}%`);
  console.log(`   - Average tree size: ${stats.averageTreeSize.toFixed(1)} nodes`);
  console.log(`   - Average depth: ${stats.averageMaxDepth.toFixed(1)}`);

  // Generate debug report
  const reflectiveAgent = agency.reflectiveAgents.get('demo-agent');
  if (reflectiveAgent) {
    const debugger = new ReflectiveDebugger(reflectiveAgent);
    const debugReport = debugger.generateDebugReport();
    
    console.log('\n🔧 Debug Information:');
    console.log(`   - Agent status: ${debugReport.agentState.status}`);
    console.log(`   - Tools available: ${debugReport.agentState.toolCount}`);
    console.log(`   - Memory entries: ${Object.keys(debugReport.memoryContents.keyValueStore).length}`);
    console.log(`   - Reasoning patterns: ${debugReport.memoryContents.reasoningPatterns.length}`);
    
    if (debugReport.recommendations.length > 0) {
      console.log('   - Debug recommendations:');
      debugReport.recommendations.forEach(rec => console.log(`     • ${rec}`));
    }
  }

  // Summary
  console.log('\n✨ Demo Summary');
  console.log('=' .repeat(60));
  console.log(`🎯 Scenarios completed: ${results.length}`);
  console.log(`✅ Successful plans: ${results.filter(r => !r.error).length}`);
  console.log(`📁 Visualizations generated: ${results.filter(r => r.filename).length}`);
  console.log(`🧠 Learning patterns stored: ${stats.totalReflectivePlans}`);
  
  console.log('\n🔗 Generated Files:');
  results.filter(r => r.filename).forEach(r => {
    console.log(`   - ${r.filename} (${r.scenario})`);
  });

  console.log('\n🎉 Agency2.js demonstration completed!');
  console.log('The tree-of-thought approach provides:');
  console.log('   • More thorough exploration of solution space');
  console.log('   • Self-evaluation and scoring of alternatives');
  console.log('   • Learning from past reasoning patterns');
  console.log('   • Rich visualization and debugging capabilities');
  console.log('   • Seamless integration with existing Agency.js workflow');

  return results;
}

// Export for use in other modules
export {
  runAgency2Demo,
  DemoLLMProvider,
  createDemoTools
};

// Run demo if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAgency2Demo().catch(console.error);
}
