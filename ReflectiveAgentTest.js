import { EnhancedAgency } from './EnhancedAgency.js';
import { createExampleReflectiveAgent } from './ReflectiveAgentExample.js';

/**
 * Comprehensive test suite for ReflectiveAgent memory and learning capabilities
 */

// Mock LLM Provider with more sophisticated responses
class AdvancedMockLLMProvider {
  constructor() {
    this.callCount = 0;
    this.responses = new Map();
  }

  async generateContent(request) {
    this.callCount++;
    const prompt = request.contents[0].parts[0].text.toLowerCase();
    
    // Store the prompt for analysis
    const promptKey = this._getPromptKey(prompt);
    if (!this.responses.has(promptKey)) {
      this.responses.set(promptKey, []);
    }
    this.responses.get(promptKey).push({
      timestamp: new Date(),
      prompt: prompt.substring(0, 100) + '...'
    });

    // Simulate learning from previous interactions
    if (prompt.includes('generate') && prompt.includes('alternative')) {
      return this._generateAlternativesResponse(prompt);
    }
    
    if (prompt.includes('rate') || prompt.includes('score')) {
      return this._generateScoreResponse(prompt);
    }
    
    if (prompt.includes('analysis') || prompt.includes('perform')) {
      return this._generateAnalysisResponse(prompt);
    }
    
    return this._generateDefaultResponse(prompt);
  }

  _getPromptKey(prompt) {
    if (prompt.includes('alternative')) return 'alternatives';
    if (prompt.includes('score')) return 'scoring';
    if (prompt.includes('analysis')) return 'analysis';
    return 'default';
  }

  _generateAlternativesResponse(prompt) {
    // Simulate more sophisticated alternatives based on context
    const alternatives = [];
    
    if (prompt.includes('software') || prompt.includes('development')) {
      alternatives.push(
        {
          "thought": "Start with requirements analysis and stakeholder interviews",
          "action": "tool:research(topic: 'requirements gathering')"
        },
        {
          "thought": "Create a technical architecture design document",
          "action": "create_technical_design"
        },
        {
          "thought": "Implement a proof of concept to validate approach",
          "action": "tool:validator(solution: 'proof of concept')"
        }
      );
    } else if (prompt.includes('business') || prompt.includes('strategy')) {
      alternatives.push(
        {
          "thought": "Conduct market research and competitive analysis",
          "action": "tool:research(topic: 'market analysis')"
        },
        {
          "thought": "Develop customer personas and journey mapping",
          "action": "analyze_customer_needs"
        },
        {
          "thought": "Create financial projections and ROI analysis",
          "action": "tool:plan_creator(goal: 'financial planning')"
        }
      );
    } else {
      alternatives.push(
        {
          "thought": "Break down the problem into smaller components",
          "action": "decompose_problem"
        },
        {
          "thought": "Research best practices and existing solutions",
          "action": "tool:research(topic: 'best practices')"
        },
        {
          "thought": "Create a systematic approach plan",
          "action": "tool:plan_creator(goal: 'systematic approach')"
        }
      );
    }

    return {
      candidates: [{
        content: {
          parts: [{ text: JSON.stringify(alternatives) }]
        }
      }]
    };
  }

  _generateScoreResponse(prompt) {
    // Simulate more nuanced scoring based on context
    let baseScore = 7.0;
    
    if (prompt.includes('research')) baseScore += 0.5;
    if (prompt.includes('validate')) baseScore += 0.8;
    if (prompt.includes('plan')) baseScore += 0.3;
    if (prompt.includes('risk')) baseScore -= 0.5;
    
    // Add some randomness but keep it reasonable
    const score = Math.min(10, Math.max(1, baseScore + (Math.random() - 0.5) * 2));
    
    return {
      candidates: [{
        content: {
          parts: [{ text: score.toFixed(1) }]
        }
      }]
    };
  }

  _generateAnalysisResponse(prompt) {
    const responses = [
      "Analysis reveals three critical success factors: stakeholder alignment, technical feasibility, and resource availability. Recommend proceeding with phased implementation approach.",
      "Comprehensive evaluation shows strong potential with moderate implementation complexity. Key risks identified include timeline constraints and integration challenges.",
      "Strategic analysis indicates high value opportunity with clear path to execution. Recommend immediate action on high-priority components."
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      candidates: [{
        content: {
          parts: [{ text: randomResponse }]
        }
      }]
    };
  }

  _generateDefaultResponse(prompt) {
    return {
      candidates: [{
        content: {
          parts: [{
            text: "I understand the request and will proceed with the appropriate next step based on the context and requirements."
          }]
        }
      }]
    };
  }

  getStats() {
    return {
      totalCalls: this.callCount,
      responseTypes: Object.fromEntries(this.responses),
      callsByType: Object.fromEntries(
        Array.from(this.responses.entries()).map(([key, calls]) => [key, calls.length])
      )
    };
  }
}

/**
 * Test memory and learning capabilities
 */
async function testMemoryAndLearning() {
  console.log('🧠 Testing Memory and Learning Capabilities');
  console.log('=' .repeat(60));

  const llmProvider = new AdvancedMockLLMProvider();
  
  // Create enhanced agency with reflective capabilities
  const agency = new EnhancedAgency({
    reflectivePlanningEnabled: true,
    maxDepth: 4,
    maxBranches: 3,
    explorationStrategy: 'best-first',
    scoringThreshold: 6.5
  });

  // Create a test agent
  const agentConfig = {
    id: 'test-agent-memory',
    name: 'Memory Test Agent',
    description: 'Agent for testing memory and learning capabilities',
    role: 'You are a learning agent that improves through experience.',
    llmProvider: llmProvider
  };

  agency.createAgent(agentConfig);
  
  // Add some tools
  const testTools = {
    memory_tool: {
      name: 'memory_tool',
      schema: {
        function_declaration: {
          name: 'memory_tool',
          description: 'Store and retrieve information from memory',
          parameters: {
            type: 'object',
            properties: {
              action: { type: 'string', enum: ['store', 'retrieve'] },
              key: { type: 'string' },
              value: { type: 'string' }
            },
            required: ['action', 'key']
          }
        }
      },
      call: async (params) => {
        if (params.action === 'store') {
          return { stored: true, key: params.key, value: params.value };
        } else {
          return { retrieved: true, key: params.key, value: `Retrieved: ${params.key}` };
        }
      }
    }
  };

  const agent = agency.agents['test-agent-memory'];
  Object.values(testTools).forEach(tool => agent.addTool(tool));

  // Test 1: Learn from similar goals
  console.log('\n📚 Test 1: Learning from Similar Goals');
  console.log('-' .repeat(40));

  const similarGoals = [
    "Design a user authentication system",
    "Create a secure login mechanism", 
    "Implement user access control"
  ];

  const learningResults = [];

  for (const goal of similarGoals) {
    console.log(`\n🎯 Planning for: ${goal}`);
    
    const result = await agency.planJobsFromGoalEnhanced('test-agent-memory', goal, {
      forceReflective: true,
      context: { iteration: learningResults.length + 1 }
    });
    
    learningResults.push({
      goal,
      result,
      timestamp: new Date()
    });
    
    console.log(`✅ Completed planning. Jobs created: ${result.jobs ? result.jobs.length : 0}`);
    
    if (result.tree) {
      console.log(`📊 Tree stats: ${result.tree.statistics.totalNodes} nodes, depth ${result.tree.statistics.maxDepth}`);
    }
  }

  // Test 2: Analyze learning patterns
  console.log('\n🔍 Test 2: Analyzing Learning Patterns');
  console.log('-' .repeat(40));

  const reflectiveAgent = agency.reflectiveAgents.get('test-agent-memory');
  if (reflectiveAgent) {
    const patterns = reflectiveAgent.getSimilarReasoningPatterns('authentication');
    console.log(`Found ${patterns.length} similar reasoning patterns`);
    
    patterns.forEach((pattern, index) => {
      console.log(`Pattern ${index + 1}:`);
      console.log(`  Goal: ${pattern.goal}`);
      console.log(`  Success: ${pattern.success}`);
      console.log(`  Nodes: ${pattern.tree.statistics.totalNodes}`);
      console.log(`  Efficiency: ${(pattern.tree.statistics.explorationEfficiency * 100).toFixed(1)}%`);
    });
  }

  // Test 3: Memory persistence and retrieval
  console.log('\n💾 Test 3: Memory Persistence and Retrieval');
  console.log('-' .repeat(40));

  const memoryScope = agency.getMemoryScope('agent:test-agent-memory');
  
  // Store some learning insights
  await memoryScope.rememberAsync('learning_insight_1', {
    insight: 'Authentication systems benefit from early security analysis',
    confidence: 0.85,
    supportingEvidence: learningResults.length,
    timestamp: new Date()
  });

  await memoryScope.rememberAsync('pattern_recognition', {
    recognizedPattern: 'Security-related goals require validation steps',
    occurrences: learningResults.filter(r => r.goal.includes('secure') || r.goal.includes('auth')).length,
    timestamp: new Date()
  });

  // Retrieve and display memory contents
  const insights = memoryScope.recall('learning_insight_1');
  const patterns = memoryScope.recall('pattern_recognition');
  
  console.log('💡 Stored Learning Insights:');
  console.log(JSON.stringify(insights, null, 2));
  console.log('\n🔄 Recognized Patterns:');
  console.log(JSON.stringify(patterns, null, 2));

  // Test 4: Performance improvement over time
  console.log('\n📈 Test 4: Performance Improvement Analysis');
  console.log('-' .repeat(40));

  const stats = agency.getReflectivePlanningStats('test-agent-memory');
  console.log('📊 Reflective Planning Statistics:');
  console.log(`  Total plans: ${stats.totalReflectivePlans}`);
  console.log(`  Success rate: ${(stats.successRate * 100).toFixed(1)}%`);
  console.log(`  Average tree size: ${stats.averageTreeSize.toFixed(1)} nodes`);
  console.log(`  Average depth: ${stats.averageMaxDepth.toFixed(1)}`);

  // Test 5: LLM usage optimization
  console.log('\n🎯 Test 5: LLM Usage Optimization');
  console.log('-' .repeat(40));

  const llmStats = llmProvider.getStats();
  console.log('🤖 LLM Provider Statistics:');
  console.log(`  Total calls: ${llmStats.totalCalls}`);
  console.log('  Calls by type:');
  Object.entries(llmStats.callsByType).forEach(([type, count]) => {
    console.log(`    ${type}: ${count} calls`);
  });

  return {
    learningResults,
    memoryContents: {
      insights,
      patterns
    },
    statistics: stats,
    llmUsage: llmStats
  };
}

/**
 * Test comparison between traditional and reflective planning
 */
async function testPlanningComparison() {
  console.log('\n⚖️  Testing Planning Approach Comparison');
  console.log('=' .repeat(60));

  const agency = new EnhancedAgency({
    reflectivePlanningEnabled: true
  });

  // Create test agent
  agency.createAgent({
    id: 'comparison-agent',
    name: 'Comparison Test Agent',
    description: 'Agent for comparing planning approaches',
    role: 'You are a planning agent.',
    llmProvider: new AdvancedMockLLMProvider()
  });

  const testGoals = [
    "Plan a simple vacation to Hawaii",
    "Design a complex enterprise software architecture",
    "Develop a comprehensive marketing strategy for a new product launch"
  ];

  for (const goal of testGoals) {
    console.log(`\n🎯 Comparing approaches for: ${goal}`);
    console.log('-' .repeat(50));

    try {
      const comparison = await agency.comparePlanningApproaches('comparison-agent', goal);
      
      console.log('📋 Traditional Planning:');
      console.log(`  Jobs: ${comparison.traditional.jobCount}`);
      console.log(`  Time: ${comparison.traditional.planningTime}ms`);
      
      console.log('🧠 Reflective Planning:');
      console.log(`  Jobs: ${comparison.reflective.jobCount}`);
      console.log(`  Time: ${comparison.reflective.planningTime}ms`);
      console.log(`  Success: ${comparison.reflective.result.success}`);
      
      if (comparison.reflective.treeStatistics) {
        console.log(`  Tree nodes: ${comparison.reflective.treeStatistics.totalNodes}`);
        console.log(`  Max depth: ${comparison.reflective.treeStatistics.maxDepth}`);
        console.log(`  Efficiency: ${(comparison.reflective.treeStatistics.explorationEfficiency * 100).toFixed(1)}%`);
      }
      
      console.log('📊 Comparison:');
      console.log(`  Time ratio: ${comparison.comparison.timeRatio.toFixed(2)}x`);
      console.log(`  Job difference: ${comparison.comparison.jobCountDifference}`);
      console.log(`  Reflective advantage: ${comparison.comparison.reflectiveAdvantage}`);
      
    } catch (error) {
      console.error(`❌ Error comparing approaches for "${goal}":`, error.message);
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting ReflectiveAgent Memory and Learning Tests\n');
  
  try {
    const memoryResults = await testMemoryAndLearning();
    await testPlanningComparison();
    
    console.log('\n✨ All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`  Learning patterns stored: ${Object.keys(memoryResults.memoryContents).length}`);
    console.log(`  Planning iterations: ${memoryResults.learningResults.length}`);
    console.log(`  LLM calls made: ${memoryResults.llmUsage.totalCalls}`);
    console.log(`  Success rate: ${(memoryResults.statistics.successRate * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    throw error;
  }
}

// Export for use in other modules
export {
  testMemoryAndLearning,
  testPlanningComparison,
  runAllTests,
  AdvancedMockLLMProvider
};

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}
