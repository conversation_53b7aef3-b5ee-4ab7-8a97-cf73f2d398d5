# Agency2.js - Tree-of-Thought Implementation

## 🎯 Overview

Agency2.js is a comprehensive enhancement to the existing Agency.js framework that introduces **tree-of-thought reasoning** capabilities. Instead of linear planning, it enables agents to explore multiple reasoning paths, self-evaluate alternatives, and learn from past experiences.

## ✨ Key Features

### 🌳 Tree-of-Thought Reasoning
- **Multi-path exploration**: Agents can explore multiple solution approaches simultaneously
- **Self-evaluation**: Each reasoning step is scored and evaluated before execution
- **Dynamic adaptation**: Agents can backtrack and try alternative approaches when needed
- **Parallel exploration**: Multiple promising paths can be explored concurrently

### 🧠 Memory & Learning
- **Pattern recognition**: Agents learn from past reasoning sessions
- **Experience storage**: Successful reasoning patterns are stored for future use
- **Adaptive improvement**: Performance improves over time through accumulated experience

### 🔧 Tool Integration
- **Seamless compatibility**: Works with existing Agency.js tools and workflows
- **Enhanced tool usage**: Tools are used more strategically within reasoning trees
- **Action execution**: Supports complex multi-step tool orchestration

### 📊 Visualization & Debugging
- **Interactive tree visualization**: HTML-based interactive reasoning tree explorer
- **Multiple formats**: Text, Mermaid diagrams, D3.js JSON, and analysis reports
- **Debug utilities**: Comprehensive debugging and performance analysis tools

### ⚡ Performance Features
- **Intelligent fallback**: Automatically chooses between reflective and traditional planning
- **Configurable depth**: Adjustable reasoning depth based on problem complexity
- **Efficiency optimization**: Built-in mechanisms to prevent infinite exploration

## 🚀 Quick Start

### Installation & Setup

```javascript
import { EnhancedAgency } from './EnhancedAgency.js';
import { ReflectiveAgent } from './ReflectiveAgent.js';

// Create enhanced agency with reflective capabilities
const agency = new EnhancedAgency({
  reflectivePlanningEnabled: true,
  maxDepth: 5,
  maxBranches: 3,
  explorationStrategy: 'best-first',
  scoringThreshold: 6.0
});

// Create an agent
agency.createAgent({
  id: 'my-agent',
  name: 'Problem Solver',
  description: 'An agent that uses tree-of-thought reasoning',
  role: 'You are an expert problem-solving agent.',
  llmProvider: yourLLMProvider
});
```

### Basic Usage

```javascript
// Enhanced planning (automatically chooses reflective vs traditional)
const result = await agency.planJobsFromGoalEnhanced(
  'my-agent',
  'Design a secure authentication system',
  {
    context: { technology: 'Node.js', users: '10000+' },
    maxDepth: 4
  }
);

// Direct reflective planning
const reflectiveAgent = agency.createReflectiveAgent('my-agent');
const reasoning = await reflectiveAgent.executeReflectivePlan(
  'Optimize customer retention strategy'
);
```

## 📁 File Structure

```
Agency2.js Implementation/
├── ReflectiveAgent.js          # Core tree-of-thought agent
├── EnhancedAgency.js           # Enhanced agency with reflective capabilities
├── ReflectiveVisualization.js  # Visualization and debugging tools
├── ReflectiveAgentExample.js   # Usage examples and demos
├── ReflectiveAgentTest.js      # Memory and learning tests
├── Agency2TestSuite.js         # Comprehensive test suite
├── Agency2Demo.js              # Full demonstration
└── idea.md                     # This documentation
```

## 🏗️ Architecture

### Core Components

1. **ThoughtNode**: Represents individual reasoning steps in the tree
2. **ReflectiveAgent**: Extends Agent with tree-of-thought capabilities
3. **EnhancedAgency**: Integrates reflective planning with existing workflows
4. **TreeVisualizer**: Generates various visualization formats
5. **ReflectiveDebugger**: Provides debugging and analysis tools

### Tree-of-Thought Process

```
Goal Input → Initial Analysis → Generate Alternatives → Score Options →
Select Best → Execute Action → Check Completion → Continue/Finish
     ↑                                                      ↓
     └─────────── Backtrack if needed ←──────────────────────┘
```

## 🔧 Configuration Options

### ReflectiveAgent Configuration

```javascript
const reflectiveAgent = new ReflectiveAgent({
  // Basic agent properties
  id: 'agent-id',
  name: 'Agent Name',
  role: 'Agent role description',
  llmProvider: yourLLMProvider,

  // Tree-of-thought specific settings
  maxDepth: 5,                    // Maximum reasoning depth
  maxBranches: 3,                 // Alternatives per step
  explorationStrategy: 'best-first', // 'best-first', 'depth-first', 'breadth-first', 'balanced'
  scoringThreshold: 6.0,          // Minimum score for viable alternatives

  // Parallel exploration
  parallelExploration: true,      // Enable parallel path exploration
  maxParallelPaths: 2,           // Max concurrent paths

  // Memory and learning
  memoryConfig: {
    maxHistoryLength: 100
  }
});
```

### EnhancedAgency Configuration

```javascript
const agency = new EnhancedAgency({
  // Enable/disable reflective planning
  reflectivePlanningEnabled: true,

  // Default reflective settings
  maxDepth: 5,
  maxBranches: 3,
  explorationStrategy: 'best-first',
  scoringThreshold: 6.0,

  // Complexity detection thresholds
  complexityThreshold: 2
});
```

## 📊 Visualization Examples

### Text Tree Visualization
```
🌳 Reasoning Tree for: "Design authentication system"
📊 Statistics: 7 nodes, depth 3, efficiency 85.7%
⏱️  Duration: 2.3s

✅ [8.5] Initial analysis of the goal: "Design authentication system"
    🎯 Action: analyze_requirements
  ✅ [7.2] Analyze security requirements and compliance standards
      🎯 Action: tool:security_analyzer(scope: 'authentication')
    ✅ [9.1] Implement OAuth 2.0 with JWT tokens
        🎯 Action: tool:oauth_implementer(type: 'JWT')
      ✅ [8.8] Design database schema for user management
          🎯 Action: create_database_design
```

### Mermaid Diagram
```mermaid
graph TD
    A["Initial analysis"] --> B["Security analysis"]
    A --> C["Database design"]
    B --> D["OAuth implementation"]
    B --> E["Compliance check"]

    class A complete
    class B complete
    class C complete
    class D complete
```

## 🧪 Testing & Validation

### Run the Test Suite

```bash
node Agency2TestSuite.js
```

### Run the Demo

```bash
node Agency2Demo.js
```

### Test Categories

- **Core Functionality**: Basic reflective reasoning, parallel exploration
- **Integration**: Agency.js compatibility, tool integration
- **Performance**: Scaling behavior, memory usage
- **Visualization**: All visualization formats
- **Reliability**: Error handling, edge cases

## 📈 Performance Comparison

### Traditional vs Reflective Planning

| Aspect | Traditional Planning | Tree-of-Thought Planning |
|--------|---------------------|--------------------------|
| **Speed** | Fast (single pass) | Slower (multiple evaluations) |
| **Quality** | Good for simple tasks | Superior for complex problems |
| **Adaptability** | Fixed sequence | Dynamic path exploration |
| **Learning** | No learning | Learns from experience |
| **Debugging** | Limited visibility | Rich visualization |
| **Self-Correction** | Error handling only | Built-in self-evaluation |

### When to Use Each Approach

**Use Traditional Planning for:**
- Simple, well-defined tasks
- Time-critical operations
- Straightforward workflows
- Resource-constrained environments

**Use Reflective Planning for:**
- Complex problem-solving
- Ambiguous requirements
- Strategic planning
- Learning-critical applications
- High-stakes decisions

## 🔍 Advanced Features

### Parallel Path Exploration

```javascript
// Enable parallel exploration of multiple promising paths
const agent = new ReflectiveAgent({
  parallelExploration: true,
  maxParallelPaths: 3,
  explorationStrategy: 'balanced'
});

// The agent will explore up to 3 paths simultaneously
const result = await agent.executeReflectivePlan(
  'Complex multi-faceted problem'
);
```

### Learning from Experience

```javascript
// Agent automatically learns from past reasoning sessions
const patterns = agent.getSimilarReasoningPatterns('authentication');
console.log(`Found ${patterns.length} similar patterns from past experience`);

// Patterns influence future reasoning decisions
```

### Custom Scoring Functions

```javascript
// Override default scoring with custom logic
const agent = new ReflectiveAgent({
  customScoring: async (goal, alternative, context) => {
    // Your custom scoring logic here
    return score; // 0-10
  }
});
```

### Interactive Debugging

```javascript
import { ReflectiveDebugger } from './ReflectiveVisualization.js';

const debugger = new ReflectiveDebugger(agent);
const report = debugger.generateDebugReport();

// Export visualization for analysis
const htmlViz = debugger.exportVisualization('html');
fs.writeFileSync('reasoning_tree.html', htmlViz);
```

## 🎯 Real-World Use Cases

### Software Development
- **System Architecture Design**: Explore multiple architectural patterns
- **Security Implementation**: Evaluate different security approaches
- **Performance Optimization**: Consider various optimization strategies

### Business Strategy
- **Market Entry Planning**: Analyze multiple market entry strategies
- **Product Development**: Explore different feature prioritization approaches
- **Customer Retention**: Develop comprehensive retention strategies

### Research & Analysis
- **Literature Review**: Systematically explore research directions
- **Data Analysis**: Consider multiple analytical approaches
- **Problem Diagnosis**: Explore various root cause hypotheses

## 🚀 Getting Started

1. **Clone or copy the implementation files**
2. **Install dependencies** (if any)
3. **Run the demo**: `node Agency2Demo.js`
4. **Run tests**: `node Agency2TestSuite.js`
5. **Integrate with your existing Agency.js setup**

## 🤝 Integration with Existing Agency.js

Agency2.js is designed as a **drop-in enhancement** to your existing Agency.js setup:

```javascript
// Minimal integration - just replace Agency with EnhancedAgency
import { EnhancedAgency } from './EnhancedAgency.js';

const agency = new EnhancedAgency({
  // Your existing configuration
  reflectivePlanningEnabled: true  // Add this line
});

// Everything else works the same, but now with tree-of-thought capabilities
```

## 📚 Key Benefits

✅ **Enhanced Problem Solving**: Systematic exploration of solution space
✅ **Self-Evaluation**: Built-in quality assessment of reasoning steps
✅ **Learning Capability**: Improves performance through experience
✅ **Rich Debugging**: Comprehensive visualization and analysis tools
✅ **Backward Compatibility**: Works with existing Agency.js workflows
✅ **Configurable Complexity**: Adapts to problem complexity automatically
✅ **Parallel Exploration**: Efficient exploration of multiple paths
✅ **Memory Integration**: Learns from past reasoning patterns

## 🔮 Future Enhancements

- **Multi-agent tree reasoning**: Collaborative tree-of-thought across multiple agents
- **Reinforcement learning**: Learn optimal exploration strategies
- **Dynamic scoring**: Adaptive scoring based on domain and context
- **Tree pruning**: Intelligent pruning of less promising branches
- **Distributed reasoning**: Scale tree exploration across multiple processes

## 📄 License & Contributing

This implementation extends the existing Agency.js framework and follows the same licensing terms. Contributions are welcome!

---

**Agency2.js** represents a significant advancement in agentic AI reasoning, bringing the power of tree-of-thought exploration to practical agent frameworks. The combination of systematic reasoning, self-evaluation, and learning capabilities makes it ideal for complex problem-solving scenarios where traditional linear planning falls short.