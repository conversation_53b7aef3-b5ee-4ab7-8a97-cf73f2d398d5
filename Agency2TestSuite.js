import { EnhancedAgency } from './EnhancedAgency.js';
import { ReflectiveAgent } from './ReflectiveAgent.js';
import { TreeVisualizer, ReflectiveDebugger } from './ReflectiveVisualization.js';
import { AdvancedMockLLMProvider } from './ReflectiveAgentTest.js';

/**
 * Comprehensive test suite for Agency2.js - Tree-of-Thought Implementation
 * Tests all major features and validates the system against various scenarios
 */

class TestRunner {
  constructor() {
    this.tests = [];
    this.results = [];
    this.startTime = null;
  }

  addTest(name, testFunction, category = 'general') {
    this.tests.push({ name, testFunction, category });
  }

  async runAll() {
    console.log('🧪 Starting Agency2.js Comprehensive Test Suite');
    console.log('=' .repeat(70));
    
    this.startTime = Date.now();
    let passed = 0;
    let failed = 0;

    for (const test of this.tests) {
      console.log(`\n🔬 Running: ${test.name} (${test.category})`);
      console.log('-' .repeat(50));
      
      try {
        const startTime = Date.now();
        const result = await test.testFunction();
        const duration = Date.now() - startTime;
        
        console.log(`✅ PASSED (${duration}ms)`);
        this.results.push({
          name: test.name,
          category: test.category,
          status: 'PASSED',
          duration,
          result
        });
        passed++;
        
      } catch (error) {
        console.log(`❌ FAILED: ${error.message}`);
        this.results.push({
          name: test.name,
          category: test.category,
          status: 'FAILED',
          error: error.message
        });
        failed++;
      }
    }

    const totalDuration = Date.now() - this.startTime;
    
    console.log('\n📊 Test Suite Summary');
    console.log('=' .repeat(70));
    console.log(`Total tests: ${this.tests.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success rate: ${((passed / this.tests.length) * 100).toFixed(1)}%`);
    console.log(`Total duration: ${totalDuration}ms`);
    
    // Group results by category
    const byCategory = this.results.reduce((acc, result) => {
      if (!acc[result.category]) acc[result.category] = [];
      acc[result.category].push(result);
      return acc;
    }, {});
    
    console.log('\n📋 Results by Category:');
    Object.entries(byCategory).forEach(([category, results]) => {
      const categoryPassed = results.filter(r => r.status === 'PASSED').length;
      console.log(`  ${category}: ${categoryPassed}/${results.length} passed`);
    });

    return {
      total: this.tests.length,
      passed,
      failed,
      successRate: passed / this.tests.length,
      duration: totalDuration,
      results: this.results
    };
  }
}

/**
 * Test utilities and helpers
 */
class TestUtils {
  static createTestAgent(config = {}) {
    return new ReflectiveAgent({
      id: 'test-agent',
      name: 'Test Agent',
      description: 'Agent for testing purposes',
      role: 'You are a test agent.',
      llmProvider: new AdvancedMockLLMProvider(),
      maxDepth: 3,
      maxBranches: 2,
      explorationStrategy: 'best-first',
      scoringThreshold: 6.0,
      parallelExploration: true,
      maxParallelPaths: 2,
      ...config
    });
  }

  static createTestAgency(config = {}) {
    return new EnhancedAgency({
      reflectivePlanningEnabled: true,
      maxDepth: 4,
      maxBranches: 3,
      explorationStrategy: 'best-first',
      scoringThreshold: 6.5,
      ...config
    });
  }

  static async assertTreeStructure(tree, expectedProperties) {
    if (!tree) throw new Error('Tree is null or undefined');
    if (!tree.nodes) throw new Error('Tree has no nodes');
    if (!tree.rootId) throw new Error('Tree has no root ID');
    
    const rootNode = tree.nodes[tree.rootId];
    if (!rootNode) throw new Error('Root node not found');
    
    if (expectedProperties.minNodes && Object.keys(tree.nodes).length < expectedProperties.minNodes) {
      throw new Error(`Expected at least ${expectedProperties.minNodes} nodes, got ${Object.keys(tree.nodes).length}`);
    }
    
    if (expectedProperties.maxDepth && tree.statistics.maxDepth > expectedProperties.maxDepth) {
      throw new Error(`Expected max depth ${expectedProperties.maxDepth}, got ${tree.statistics.maxDepth}`);
    }
    
    return true;
  }

  static async measurePerformance(operation) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    
    const result = await operation();
    
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    
    return {
      result,
      duration: endTime - startTime,
      memoryDelta: {
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal
      }
    };
  }
}

/**
 * Core functionality tests
 */
async function testBasicReflectiveAgent() {
  const agent = TestUtils.createTestAgent();
  
  const result = await agent.executeReflectivePlan('Test goal: solve a simple problem');
  
  if (!result.success) {
    throw new Error('Reflective planning failed');
  }
  
  await TestUtils.assertTreeStructure(result.tree, { minNodes: 2, maxDepth: 5 });
  
  return { nodeCount: result.tree.statistics.totalNodes, success: true };
}

async function testParallelExploration() {
  const agent = TestUtils.createTestAgent({
    parallelExploration: true,
    maxParallelPaths: 3,
    maxBranches: 4
  });
  
  const result = await agent.executeReflectivePlan('Complex goal requiring multiple approaches');
  
  if (!result.success) {
    throw new Error('Parallel exploration failed');
  }
  
  // Check that multiple paths were explored
  const nodes = Object.values(result.tree.nodes);
  const nodesWithChildren = nodes.filter(n => n.childrenIds && n.childrenIds.length > 1);
  
  if (nodesWithChildren.length === 0) {
    throw new Error('No parallel exploration detected');
  }
  
  return { 
    parallelNodes: nodesWithChildren.length,
    totalNodes: nodes.length,
    efficiency: result.tree.statistics.explorationEfficiency
  };
}

async function testMemoryIntegration() {
  const agent = TestUtils.createTestAgent();
  
  // Execute multiple planning sessions
  await agent.executeReflectivePlan('First goal: authentication system');
  await agent.executeReflectivePlan('Second goal: secure authentication');
  
  // Check that patterns were learned
  const patterns = agent.getSimilarReasoningPatterns('authentication');
  
  if (patterns.length === 0) {
    throw new Error('No reasoning patterns were stored');
  }
  
  return { patternsStored: patterns.length };
}

async function testToolIntegration() {
  const agent = TestUtils.createTestAgent();
  
  // Add a test tool
  agent.addTool({
    name: 'test_tool',
    schema: {
      function_declaration: {
        name: 'test_tool',
        description: 'A test tool',
        parameters: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          },
          required: ['input']
        }
      }
    },
    call: async (params) => ({ output: `Processed: ${params.input}` })
  });
  
  const result = await agent.executeReflectivePlan('Use the test tool to process data');
  
  if (!result.success) {
    throw new Error('Tool integration failed');
  }
  
  // Check that tool was used in reasoning
  const nodes = Object.values(result.tree.nodes);
  const toolUsage = nodes.some(node => 
    node.action && node.action.includes('test_tool')
  );
  
  return { toolUsed: toolUsage, nodeCount: nodes.length };
}

async function testVisualization() {
  const agent = TestUtils.createTestAgent();
  const result = await agent.executeReflectivePlan('Test visualization capabilities');
  
  const visualizer = new TreeVisualizer();
  
  // Test different visualization formats
  const textTree = visualizer.generateTextTree(result.tree);
  const mermaidDiagram = visualizer.generateMermaidDiagram(result.tree);
  const d3Json = visualizer.generateD3Json(result.tree);
  const analysisReport = visualizer.generateAnalysisReport(result.tree);
  const htmlViz = visualizer.generateInteractiveHTML(result.tree);
  
  if (!textTree || textTree.length < 50) {
    throw new Error('Text visualization too short');
  }
  
  if (!mermaidDiagram.includes('graph TD')) {
    throw new Error('Invalid Mermaid diagram');
  }
  
  if (!d3Json.name) {
    throw new Error('Invalid D3 JSON structure');
  }
  
  if (!analysisReport.overview) {
    throw new Error('Invalid analysis report');
  }
  
  if (!htmlViz.includes('<html>')) {
    throw new Error('Invalid HTML visualization');
  }
  
  return {
    textLength: textTree.length,
    mermaidValid: true,
    d3Valid: true,
    analysisValid: true,
    htmlValid: true
  };
}

async function testAgencyIntegration() {
  const agency = TestUtils.createTestAgency();
  
  // Create test agent
  agency.createAgent({
    id: 'integration-test-agent',
    name: 'Integration Test Agent',
    description: 'Agent for testing agency integration',
    role: 'You are an integration test agent.',
    llmProvider: new AdvancedMockLLMProvider()
  });
  
  // Test enhanced planning
  const result = await agency.planJobsFromGoalEnhanced(
    'integration-test-agent',
    'Complex integration test goal',
    { forceReflective: true }
  );
  
  if (result.type !== 'reflective_planning') {
    throw new Error('Enhanced planning did not use reflective approach');
  }
  
  if (!result.jobs || result.jobs.length === 0) {
    throw new Error('No jobs were created from reflective planning');
  }
  
  return {
    jobCount: result.jobs.length,
    treeNodes: result.tree.statistics.totalNodes,
    success: result.success
  };
}

async function testPerformanceComparison() {
  const agency = TestUtils.createTestAgency();
  
  agency.createAgent({
    id: 'perf-test-agent',
    name: 'Performance Test Agent',
    description: 'Agent for performance testing',
    role: 'You are a performance test agent.',
    llmProvider: new AdvancedMockLLMProvider()
  });
  
  const goal = 'Performance test goal with moderate complexity';
  
  // Measure traditional planning
  const traditionalPerf = await TestUtils.measurePerformance(async () => {
    return await agency.planJobsFromGoal('perf-test-agent', goal);
  });
  
  // Measure reflective planning
  const reflectivePerf = await TestUtils.measurePerformance(async () => {
    return await agency.planJobsFromGoalEnhanced('perf-test-agent', goal, { forceReflective: true });
  });
  
  return {
    traditional: {
      duration: traditionalPerf.duration,
      jobCount: traditionalPerf.result.length,
      memoryUsed: traditionalPerf.memoryDelta.heapUsed
    },
    reflective: {
      duration: reflectivePerf.duration,
      jobCount: reflectivePerf.result.jobs ? reflectivePerf.result.jobs.length : 0,
      memoryUsed: reflectivePerf.memoryDelta.heapUsed,
      treeNodes: reflectivePerf.result.tree ? reflectivePerf.result.tree.statistics.totalNodes : 0
    },
    speedRatio: reflectivePerf.duration / traditionalPerf.duration
  };
}

async function testErrorHandling() {
  const agent = TestUtils.createTestAgent({
    llmProvider: {
      generateContent: async () => {
        throw new Error('Simulated LLM failure');
      }
    }
  });
  
  try {
    await agent.executeReflectivePlan('This should fail gracefully');
    throw new Error('Expected error was not thrown');
  } catch (error) {
    if (error.message === 'Expected error was not thrown') {
      throw error;
    }
    // Expected error occurred
    return { errorHandled: true, errorMessage: error.message };
  }
}

async function testScalingBehavior() {
  const results = [];
  
  for (const depth of [2, 3, 4, 5]) {
    const agent = TestUtils.createTestAgent({ maxDepth: depth });
    
    const perf = await TestUtils.measurePerformance(async () => {
      return await agent.executeReflectivePlan(`Scaling test with depth ${depth}`);
    });
    
    results.push({
      depth,
      duration: perf.duration,
      nodeCount: perf.result.tree.statistics.totalNodes,
      memoryUsed: perf.memoryDelta.heapUsed
    });
  }
  
  // Check that performance scales reasonably
  const maxDuration = Math.max(...results.map(r => r.duration));
  const minDuration = Math.min(...results.map(r => r.duration));
  
  if (maxDuration / minDuration > 10) {
    throw new Error('Performance scaling is too poor');
  }
  
  return { scalingResults: results, scalingRatio: maxDuration / minDuration };
}

/**
 * Run the complete test suite
 */
async function runComprehensiveTests() {
  const runner = new TestRunner();
  
  // Core functionality tests
  runner.addTest('Basic Reflective Agent', testBasicReflectiveAgent, 'core');
  runner.addTest('Parallel Exploration', testParallelExploration, 'core');
  runner.addTest('Memory Integration', testMemoryIntegration, 'core');
  runner.addTest('Tool Integration', testToolIntegration, 'core');
  
  // Visualization and debugging tests
  runner.addTest('Visualization Generation', testVisualization, 'visualization');
  
  // Integration tests
  runner.addTest('Agency Integration', testAgencyIntegration, 'integration');
  
  // Performance tests
  runner.addTest('Performance Comparison', testPerformanceComparison, 'performance');
  runner.addTest('Scaling Behavior', testScalingBehavior, 'performance');
  
  // Error handling tests
  runner.addTest('Error Handling', testErrorHandling, 'reliability');
  
  return await runner.runAll();
}

// Export for use in other modules
export {
  runComprehensiveTests,
  TestRunner,
  TestUtils,
  testBasicReflectiveAgent,
  testParallelExploration,
  testMemoryIntegration,
  testToolIntegration,
  testVisualization,
  testAgencyIntegration,
  testPerformanceComparison,
  testErrorHandling,
  testScalingBehavior
};

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runComprehensiveTests()
    .then(results => {
      console.log('\n🎉 Test suite completed!');
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}
