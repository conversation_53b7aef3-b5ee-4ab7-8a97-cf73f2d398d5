#!/usr/bin/env node

/**
 * Setup script for Agency2.js - Tree-of-Thought Implementation
 * Helps users configure and test their Gemini integration
 */

import { GeminiProvider } from './GeminiProvider.js';
import { createGeminiReflectiveAgent } from './ReflectiveAgentExample.js';
import readline from 'readline';
import fs from 'fs';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function testGeminiConnection(apiKey, model = 'gemini-2.0-flash-exp') {
  try {
    console.log('🔍 Testing Gemini connection...');
    
    const provider = new GeminiProvider(apiKey, model);
    const response = await provider.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: 'Hello! Please respond with "Connection successful" if you can read this.' }]
      }],
      temperature: 0.3,
      maxOutputTokens: 50
    });

    const responseText = response.candidates[0].content.parts[0].text;
    console.log(`✅ Gemini response: ${responseText}`);
    return true;
    
  } catch (error) {
    console.error(`❌ Connection failed: ${error.message}`);
    return false;
  }
}

async function testReflectiveAgent(apiKey) {
  try {
    console.log('\n🧠 Testing ReflectiveAgent with simple reasoning...');
    
    const agent = createGeminiReflectiveAgent(apiKey, {
      maxDepth: 2,
      maxBranches: 2,
      scoringThreshold: 6.0
    });

    const result = await agent.executeReflectivePlan(
      'Plan a simple weekend project to organize my home office',
      { maxDepth: 2 }
    );

    console.log(`✅ Reflective planning ${result.success ? 'successful' : 'failed'}`);
    console.log(`📊 Generated ${result.tree.statistics.totalNodes} reasoning nodes`);
    console.log(`🎯 Efficiency: ${(result.tree.statistics.explorationEfficiency * 100).toFixed(1)}%`);
    
    if (result.reasoningPath && result.reasoningPath.length > 0) {
      console.log('\n💭 Sample reasoning step:');
      const step = result.reasoningPath[0];
      console.log(`   Thought: ${step.thought.substring(0, 80)}...`);
      console.log(`   Score: ${step.score}/10`);
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ ReflectiveAgent test failed: ${error.message}`);
    return false;
  }
}

async function createEnvFile(apiKey) {
  const envContent = `# Agency2.js Configuration
GEMINI_API_KEY=${apiKey}

# Optional: Customize model (default: gemini-2.0-flash-exp)
# GEMINI_MODEL=gemini-2.0-flash-exp

# Optional: Enable debug logging
# DEBUG=true
`;

  try {
    fs.writeFileSync('.env', envContent);
    console.log('✅ Created .env file with your API key');
    console.log('💡 Add .env to your .gitignore to keep your API key secure');
    return true;
  } catch (error) {
    console.error(`❌ Failed to create .env file: ${error.message}`);
    return false;
  }
}

async function showQuickStart() {
  console.log('\n🚀 Quick Start Guide:');
  console.log('=' .repeat(50));
  
  console.log('\n1. Basic ReflectiveAgent usage:');
  console.log(`
import { createGeminiReflectiveAgent } from './ReflectiveAgentExample.js';

const agent = createGeminiReflectiveAgent(process.env.GEMINI_API_KEY);
const result = await agent.executeReflectivePlan('Your complex goal here');
console.log(result);
`);

  console.log('\n2. Enhanced Agency integration:');
  console.log(`
import { EnhancedAgency } from './EnhancedAgency.js';

const agency = new EnhancedAgency({ reflectivePlanningEnabled: true });
// ... create agents ...
const result = await agency.planJobsFromGoalEnhanced('agent-id', 'complex goal');
`);

  console.log('\n3. Run the full demo:');
  console.log('   node GeminiReflectiveDemo.js');
  
  console.log('\n4. Run examples:');
  console.log('   node ReflectiveAgentExample.js');
  
  console.log('\n5. Run tests:');
  console.log('   node Agency2TestSuite.js');
}

async function main() {
  console.log('🎯 Agency2.js Setup - Tree-of-Thought with Gemini');
  console.log('=' .repeat(60));
  console.log('This script will help you set up and test Agency2.js with Google Gemini.\n');

  // Check if API key is already set
  let apiKey = process.env.GEMINI_API_KEY;
  
  if (apiKey) {
    console.log('🔑 Found existing GEMINI_API_KEY environment variable');
    const useExisting = await question('Use existing API key? (y/n): ');
    
    if (useExisting.toLowerCase() !== 'y') {
      apiKey = null;
    }
  }

  // Get API key if not available
  if (!apiKey) {
    console.log('\n📝 Please provide your Gemini API key:');
    console.log('💡 Get one at: https://makersuite.google.com/app/apikey');
    apiKey = await question('Enter your Gemini API key: ');
    
    if (!apiKey || apiKey.trim().length === 0) {
      console.log('❌ API key is required. Exiting...');
      rl.close();
      process.exit(1);
    }
  }

  // Test connection
  console.log('\n🔧 Testing setup...');
  const connectionOk = await testGeminiConnection(apiKey);
  
  if (!connectionOk) {
    console.log('\n❌ Setup failed. Please check your API key and try again.');
    rl.close();
    process.exit(1);
  }

  // Test ReflectiveAgent
  const agentOk = await testReflectiveAgent(apiKey);
  
  if (!agentOk) {
    console.log('\n⚠️  Basic connection works, but ReflectiveAgent test failed.');
    console.log('This might be due to API quota limits or model availability.');
  }

  // Offer to create .env file
  if (!process.env.GEMINI_API_KEY) {
    const createEnv = await question('\nCreate .env file with your API key? (y/n): ');
    if (createEnv.toLowerCase() === 'y') {
      await createEnvFile(apiKey);
    }
  }

  // Show next steps
  console.log('\n✅ Setup completed successfully!');
  
  const showGuide = await question('\nShow quick start guide? (y/n): ');
  if (showGuide.toLowerCase() === 'y') {
    await showQuickStart();
  }

  console.log('\n🎉 You\'re ready to use Agency2.js with Gemini!');
  console.log('\n📚 Available demos:');
  console.log('   • node GeminiReflectiveDemo.js - Full strategic planning demo');
  console.log('   • node ReflectiveAgentExample.js - Basic examples');
  console.log('   • node Agency2TestSuite.js - Comprehensive tests');
  
  console.log('\n📖 Documentation: See idea.md for complete guide');
  
  rl.close();
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('\n💥 Unexpected error:', error.message);
  rl.close();
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  rl.close();
  process.exit(0);
});

// Run setup
main().catch(error => {
  console.error('\n💥 Setup failed:', error.message);
  rl.close();
  process.exit(1);
});
