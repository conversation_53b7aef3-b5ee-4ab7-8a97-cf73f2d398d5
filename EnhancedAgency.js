import { Agency } from './Agency.js';
import { ReflectiveAgent } from './ReflectiveAgent.js';

/**
 * Enhanced Agency class that integrates ReflectiveAgent for advanced planning
 * Extends the existing Agency.js with tree-of-thought capabilities
 */
export class EnhancedAgency extends Agency {
  constructor(config = {}) {
    super(config);
    
    // Configuration for reflective planning
    this.reflectivePlanningEnabled = config.reflectivePlanningEnabled !== false;
    this.reflectiveConfig = {
      maxDepth: config.maxDepth || 5,
      maxBranches: config.maxBranches || 3,
      explorationStrategy: config.explorationStrategy || 'best-first',
      scoringThreshold: config.scoringThreshold || 6.0,
      ...config.reflectiveConfig
    };
    
    // Track reflective agents
    this.reflectiveAgents = new Map();
  }

  /**
   * Create a reflective agent from an existing agent
   * @param {string} agentId - ID of the existing agent
   * @param {Object} reflectiveConfig - Configuration for reflective capabilities
   * @returns {ReflectiveAgent} - The enhanced reflective agent
   */
  createReflectiveAgent(agentId, reflectiveConfig = {}) {
    const existingAgent = this.agents[agentId];
    if (!existingAgent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // Create reflective agent with existing agent's configuration
    const reflectiveAgent = new ReflectiveAgent({
      ...existingAgent,
      ...this.reflectiveConfig,
      ...reflectiveConfig
    });

    // Copy tools from existing agent
    Object.values(existingAgent.tools).forEach(tool => {
      reflectiveAgent.addTool(tool);
    });

    // Store the reflective agent
    this.reflectiveAgents.set(agentId, reflectiveAgent);
    
    return reflectiveAgent;
  }

  /**
   * Enhanced planning method that uses ReflectiveAgent for complex goals
   * Falls back to traditional planning for simple goals
   * @param {string} agentId - ID of the agent
   * @param {string} goal - The goal to plan for
   * @param {Object} options - Planning options
   * @returns {Promise<Array>} - Array of planned jobs or reflective result
   */
  async planJobsFromGoalEnhanced(agentId, goal, options = {}) {
    const agent = this.agents[agentId];
    if (!agent) throw new Error(`Agent ${agentId} not found`);

    // Determine if we should use reflective planning
    const useReflectivePlanning = this._shouldUseReflectivePlanning(goal, options);
    
    if (useReflectivePlanning && this.reflectivePlanningEnabled) {
      console.log(`🧠 Using reflective planning for goal: ${goal}`);
      return await this._planWithReflectiveAgent(agentId, goal, options);
    } else {
      console.log(`📋 Using traditional planning for goal: ${goal}`);
      return await this.planJobsFromGoal(agentId, goal);
    }
  }

  /**
   * Determine if reflective planning should be used based on goal complexity
   * @param {string} goal - The goal to analyze
   * @param {Object} options - Planning options
   * @returns {boolean} - True if reflective planning should be used
   * @private
   */
  _shouldUseReflectivePlanning(goal, options) {
    // Force reflective planning if explicitly requested
    if (options.forceReflective) return true;
    if (options.forceTraditional) return false;

    // Analyze goal complexity
    const complexityIndicators = [
      goal.toLowerCase().includes('complex'),
      goal.toLowerCase().includes('strategy'),
      goal.toLowerCase().includes('design'),
      goal.toLowerCase().includes('analyze'),
      goal.toLowerCase().includes('optimize'),
      goal.toLowerCase().includes('research'),
      goal.split(' ').length > 10, // Long goals tend to be complex
      goal.includes('multiple') || goal.includes('various'),
      goal.includes('consider') || goal.includes('evaluate')
    ];

    const complexityScore = complexityIndicators.filter(Boolean).length;
    
    // Use reflective planning if complexity score is 2 or higher
    return complexityScore >= 2;
  }

  /**
   * Plan using ReflectiveAgent
   * @param {string} agentId - ID of the agent
   * @param {string} goal - The goal to plan for
   * @param {Object} options - Planning options
   * @returns {Promise<Object>} - Reflective planning result
   * @private
   */
  async _planWithReflectiveAgent(agentId, goal, options) {
    // Get or create reflective agent
    let reflectiveAgent = this.reflectiveAgents.get(agentId);
    if (!reflectiveAgent) {
      reflectiveAgent = this.createReflectiveAgent(agentId, options.reflectiveConfig);
    }

    try {
      // Execute reflective planning
      const reflectiveResult = await reflectiveAgent.executeReflectivePlan(goal, {
        context: options.context || {},
        maxDepth: options.maxDepth || this.reflectiveConfig.maxDepth
      });

      // Convert reflective result to traditional job format if needed
      if (options.convertToJobs !== false) {
        const jobs = this._convertReflectiveResultToJobs(reflectiveResult, goal, agentId);
        
        // Store the original reflective result in memory
        const agentMemory = this.getMemoryScope(`agent:${agentId}`);
        agentMemory.remember(`reflective_plan:${Date.now()}`, {
          goal,
          reflectiveResult,
          jobs: jobs.map(job => job.jobId),
          timestamp: new Date()
        });

        return {
          type: 'reflective_planning',
          jobs,
          reflectiveResult,
          tree: reflectiveResult.tree,
          success: reflectiveResult.success
        };
      }

      return reflectiveResult;

    } catch (error) {
      console.error('Error in reflective planning, falling back to traditional:', error);
      // Fallback to traditional planning
      return await this.planJobsFromGoal(agentId, goal);
    }
  }

  /**
   * Convert reflective planning result to traditional Agency jobs
   * @param {Object} reflectiveResult - Result from reflective planning
   * @param {string} goal - Original goal
   * @param {string} agentId - Agent ID
   * @returns {Array} - Array of job objects
   * @private
   */
  _convertReflectiveResultToJobs(reflectiveResult, goal, agentId) {
    if (!reflectiveResult.success || !reflectiveResult.reasoningPath) {
      return [];
    }

    const jobs = [];
    
    reflectiveResult.reasoningPath.forEach((step, index) => {
      // Skip final answer steps as they don't need separate jobs
      if (step.action && step.action.toLowerCase().includes('finalanswer')) {
        return;
      }

      const jobId = `${agentId}-reflective-${Date.now()}-${index}`;
      
      const brief = {
        goal,
        taskName: `Reflective Step ${index + 1}`,
        overview: step.thought,
        action: step.action,
        expectedResult: step.result,
        score: step.score,
        instructions: `Execute this reasoning step as part of the goal: "${goal}". 
                      Thought process: ${step.thought}
                      Action to take: ${step.action || 'Continue analysis'}
                      This step was scored ${step.score}/10 in the planning phase.`,
        metadata: {
          isReflectiveStep: true,
          originalNodeId: step.id,
          depth: step.depth,
          parentNodeId: step.parentId
        }
      };

      this.createBrief(jobId, brief);
      this.assignJob(jobId, agentId, "agent");
      
      jobs.push({
        jobId,
        brief,
        step: step,
        index
      });
    });

    return jobs;
  }

  /**
   * Enhanced workflow execution that can handle reflective planning results
   * @param {Object} workflowDefinition - Workflow definition (can include reflective results)
   * @param {string} workflowId - Workflow ID
   * @param {Object} initialData - Initial data
   * @returns {Promise<Object>} - Workflow execution result
   */
  async executeEnhancedWorkflow(workflowDefinition, workflowId, initialData = {}) {
    // Check if this is a reflective workflow
    if (workflowDefinition.type === 'reflective_planning') {
      return await this._executeReflectiveWorkflow(workflowDefinition, workflowId, initialData);
    }
    
    // Use traditional workflow execution
    return await this.executeWorkflow(workflowDefinition, workflowId, initialData);
  }

  /**
   * Execute a workflow based on reflective planning
   * @param {Object} reflectiveWorkflow - Reflective workflow definition
   * @param {string} workflowId - Workflow ID
   * @param {Object} initialData - Initial data
   * @returns {Promise<Object>} - Execution result
   * @private
   */
  async _executeReflectiveWorkflow(reflectiveWorkflow, workflowId, initialData) {
    const { jobs, reflectiveResult } = reflectiveWorkflow;
    
    console.log(`🧠 Executing reflective workflow: ${workflowId}`);
    console.log(`📊 Tree statistics:`, reflectiveResult.tree.statistics);
    
    // Create traditional workflow definition from reflective jobs
    const traditionalWorkflow = jobs.map(job => ({
      jobId: job.jobId,
      assigneeId: job.brief.goal.split('-')[0], // Extract agent ID
      assigneeType: "agent",
      brief: job.brief,
      metadata: {
        ...job.brief.metadata,
        reflectiveWorkflow: true,
        originalTree: reflectiveResult.tree
      }
    }));

    // Execute using traditional workflow system
    const result = await this.executeWorkflow(traditionalWorkflow, workflowId, initialData);
    
    // Enhance result with reflective information
    return {
      ...result,
      type: 'reflective_execution',
      originalTree: reflectiveResult.tree,
      reasoningPath: reflectiveResult.reasoningPath,
      treeStatistics: reflectiveResult.tree.statistics
    };
  }

  /**
   * Get reflective planning statistics for an agent
   * @param {string} agentId - Agent ID
   * @returns {Object} - Statistics about reflective planning usage
   */
  getReflectivePlanningStats(agentId) {
    const agentMemory = this.getMemoryScope(`agent:${agentId}`);
    const reflectivePlans = Object.entries(agentMemory.keyValueStore)
      .filter(([key]) => key.startsWith('reflective_plan:'))
      .map(([, value]) => value);

    const totalPlans = reflectivePlans.length;
    const successfulPlans = reflectivePlans.filter(plan => plan.reflectiveResult.success).length;
    
    const avgTreeSize = totalPlans > 0 ? 
      reflectivePlans.reduce((sum, plan) => sum + plan.reflectiveResult.tree.statistics.totalNodes, 0) / totalPlans : 0;
    
    const avgDepth = totalPlans > 0 ?
      reflectivePlans.reduce((sum, plan) => sum + plan.reflectiveResult.tree.statistics.maxDepth, 0) / totalPlans : 0;

    return {
      totalReflectivePlans: totalPlans,
      successfulPlans,
      successRate: totalPlans > 0 ? successfulPlans / totalPlans : 0,
      averageTreeSize: avgTreeSize,
      averageMaxDepth: avgDepth,
      recentPlans: reflectivePlans.slice(-5) // Last 5 plans
    };
  }

  /**
   * Compare traditional vs reflective planning for a goal
   * @param {string} agentId - Agent ID
   * @param {string} goal - Goal to plan for
   * @returns {Promise<Object>} - Comparison results
   */
  async comparePlanningApproaches(agentId, goal) {
    console.log(`🔍 Comparing planning approaches for: ${goal}`);
    
    const startTime = Date.now();
    
    // Traditional planning
    const traditionalStart = Date.now();
    const traditionalResult = await this.planJobsFromGoal(agentId, goal);
    const traditionalTime = Date.now() - traditionalStart;
    
    // Reflective planning
    const reflectiveStart = Date.now();
    const reflectiveResult = await this._planWithReflectiveAgent(agentId, goal, { convertToJobs: true });
    const reflectiveTime = Date.now() - reflectiveStart;
    
    const totalTime = Date.now() - startTime;
    
    return {
      goal,
      traditional: {
        result: traditionalResult,
        jobCount: traditionalResult.length,
        planningTime: traditionalTime
      },
      reflective: {
        result: reflectiveResult,
        jobCount: reflectiveResult.jobs ? reflectiveResult.jobs.length : 0,
        planningTime: reflectiveTime,
        treeStatistics: reflectiveResult.tree ? reflectiveResult.tree.statistics : null
      },
      comparison: {
        totalTime,
        timeRatio: reflectiveTime / traditionalTime,
        jobCountDifference: (reflectiveResult.jobs ? reflectiveResult.jobs.length : 0) - traditionalResult.length,
        reflectiveAdvantage: reflectiveResult.success && reflectiveResult.tree
      }
    };
  }
}
