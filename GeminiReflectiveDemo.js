import { EnhancedAgency } from './EnhancedAgency.js';
import { createGeminiReflectiveAgent } from './ReflectiveAgentExample.js';
import { TreeVisualizer } from './ReflectiveVisualization.js';
import { GeminiProvider } from './GeminiProvider.js';
import fs from 'fs';

/**
 * Practical demonstration of Agency2.js with real Gemini LLM
 * Shows how to integrate tree-of-thought reasoning into real-world workflows
 */

/**
 * Create real-world tools for the demonstration
 */
function createRealWorldTools() {
  return {
    web_research: {
      name: 'web_research',
      schema: {
        function_declaration: {
          name: 'web_research',
          description: 'Research information on the web about a specific topic',
          parameters: {
            type: 'object',
            properties: {
              query: { type: 'string', description: 'Search query' },
              focus: { type: 'string', description: 'Research focus area' }
            },
            required: ['query']
          }
        }
      },
      call: async (params) => {
        // Simulate web research results
        return {
          query: params.query,
          results: [
            `Latest trends in ${params.query}`,
            `Best practices for ${params.query}`,
            `Industry analysis of ${params.query}`,
            `Expert opinions on ${params.query}`
          ],
          sources: ['industry-report.com', 'expert-blog.com', 'research-paper.pdf'],
          confidence: 0.85,
          timestamp: new Date()
        };
      }
    },

    cost_analyzer: {
      name: 'cost_analyzer',
      schema: {
        function_declaration: {
          name: 'cost_analyzer',
          description: 'Analyze costs and ROI for a proposed solution',
          parameters: {
            type: 'object',
            properties: {
              solution: { type: 'string', description: 'Solution to analyze' },
              timeframe: { type: 'string', description: 'Analysis timeframe' }
            },
            required: ['solution']
          }
        }
      },
      call: async (params) => {
        return {
          solution: params.solution,
          timeframe: params.timeframe || '12 months',
          estimatedCost: '$50,000 - $150,000',
          expectedROI: '250% - 400%',
          paybackPeriod: '8-12 months',
          riskFactors: ['Market volatility', 'Technical complexity', 'Resource availability'],
          confidence: 0.78,
          timestamp: new Date()
        };
      }
    },

    technical_feasibility: {
      name: 'technical_feasibility',
      schema: {
        function_declaration: {
          name: 'technical_feasibility',
          description: 'Assess technical feasibility of a proposed solution',
          parameters: {
            type: 'object',
            properties: {
              technology: { type: 'string', description: 'Technology or approach to assess' },
              requirements: { type: 'array', items: { type: 'string' }, description: 'Technical requirements' }
            },
            required: ['technology']
          }
        }
      },
      call: async (params) => {
        return {
          technology: params.technology,
          feasibilityScore: 8.2,
          technicalRisks: ['Scalability challenges', 'Integration complexity'],
          requiredSkills: ['Cloud architecture', 'DevOps', 'Security'],
          estimatedEffort: '6-9 months',
          alternatives: ['Alternative approach A', 'Alternative approach B'],
          recommendation: 'Proceed with phased implementation',
          timestamp: new Date()
        };
      }
    }
  };
}

/**
 * Main demonstration function
 */
async function runGeminiReflectiveDemo() {
  console.log('🚀 Agency2.js with Real Gemini LLM - Practical Demonstration');
  console.log('=' .repeat(70));
  
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error('❌ GEMINI_API_KEY environment variable is required');
    console.log('💡 Set your Gemini API key: export GEMINI_API_KEY="your-api-key"');
    process.exit(1);
  }

  try {
    // Create enhanced agency with real Gemini
    const agency = new EnhancedAgency({
      reflectivePlanningEnabled: true,
      maxDepth: 4,
      maxBranches: 3,
      explorationStrategy: 'best-first',
      scoringThreshold: 7.0
    });

    // Create agent with Gemini provider
    const geminiProvider = new GeminiProvider(apiKey, 'gemini-2.0-flash-exp');
    
    agency.createAgent({
      id: 'gemini-strategic-agent',
      name: 'Gemini Strategic Planning Agent',
      description: 'Strategic planning agent powered by Gemini with tree-of-thought reasoning',
      role: `You are an expert strategic planning agent that uses systematic tree-of-thought reasoning. 
             You excel at breaking down complex business and technical challenges into manageable components,
             exploring multiple solution approaches, and providing well-reasoned recommendations.
             Always consider multiple perspectives and evaluate alternatives thoroughly.`,
      llmProvider: geminiProvider,
      llmConfig: {
        temperature: 0.8,
        maxOutputTokens: 2048,
        topP: 0.95
      },
      goals: [
        'Provide comprehensive strategic analysis',
        'Explore multiple solution approaches systematically',
        'Consider both technical and business perspectives',
        'Deliver actionable, well-reasoned recommendations'
      ]
    });

    // Add real-world tools
    const tools = createRealWorldTools();
    const agent = agency.agents['gemini-strategic-agent'];
    Object.values(tools).forEach(tool => agent.addTool(tool));

    console.log(`✅ Created agent: ${agent.name}`);
    console.log(`🔧 Tools: ${Object.keys(tools).join(', ')}`);
    console.log(`🧠 Model: ${geminiProvider.modelName}\n`);

    // Real-world scenario: Digital transformation strategy
    const goal = `Develop a comprehensive digital transformation strategy for a traditional manufacturing company 
                  with 500 employees, $50M annual revenue, looking to modernize operations and enter e-commerce markets`;

    console.log('🎯 Strategic Planning Challenge:');
    console.log(goal);
    console.log('\n🧠 Using tree-of-thought reasoning with Gemini...\n');

    const startTime = Date.now();
    
    // Use enhanced planning with reflective reasoning
    const planningResult = await agency.planJobsFromGoalEnhanced(
      'gemini-strategic-agent',
      goal,
      {
        context: {
          company: {
            industry: 'Manufacturing',
            employees: 500,
            revenue: '$50M',
            currentTech: 'Legacy systems',
            goals: ['Modernization', 'E-commerce entry', 'Efficiency']
          },
          constraints: {
            budget: '$2M',
            timeline: '18 months',
            riskTolerance: 'Medium'
          }
        },
        forceReflective: true,
        maxDepth: 4
      }
    );

    const planningTime = Date.now() - startTime;

    console.log('✅ Strategic Planning Completed!');
    console.log(`⏱️  Planning time: ${(planningTime / 1000).toFixed(1)}s`);
    console.log(`📊 Result type: ${planningResult.type}`);
    console.log(`🔢 Strategic initiatives: ${planningResult.jobs ? planningResult.jobs.length : 0}`);

    if (planningResult.tree) {
      const stats = planningResult.tree.statistics;
      console.log(`🌳 Reasoning tree statistics:`);
      console.log(`   - Total reasoning nodes: ${stats.totalNodes}`);
      console.log(`   - Maximum depth explored: ${stats.maxDepth}`);
      console.log(`   - Exploration efficiency: ${(stats.explorationEfficiency * 100).toFixed(1)}%`);
      console.log(`   - Average solution quality: ${stats.averageScore.toFixed(1)}/10`);
      console.log(`   - Successful paths: ${stats.completedNodes}/${stats.totalNodes}`);
    }

    // Show the reasoning process
    if (planningResult.success && planningResult.reasoningPath) {
      console.log('\n🧠 Key Strategic Reasoning Steps:');
      planningResult.reasoningPath.forEach((step, index) => {
        console.log(`\n${index + 1}. 💭 ${step.thought}`);
        if (step.action) {
          console.log(`   🎯 Action: ${step.action}`);
        }
        if (step.result && typeof step.result === 'object' && !step.result.error) {
          console.log(`   ✅ Result: ${JSON.stringify(step.result).substring(0, 100)}...`);
        }
        console.log(`   📊 Quality Score: ${step.score}/10`);
      });
    }

    // Generate comprehensive visualization
    const visualizer = new TreeVisualizer({
      showScores: true,
      showActions: true,
      showTimestamps: true
    });

    console.log('\n🌳 Complete Reasoning Tree:');
    console.log(visualizer.generateTextTree(planningResult.tree));

    // Generate analysis report
    const analysis = visualizer.generateAnalysisReport(planningResult.tree);
    console.log('\n📊 Strategic Planning Analysis:');
    console.log(`   - Solution paths explored: ${analysis.pathAnalysis.totalPaths}`);
    console.log(`   - Most efficient path: ${analysis.pathAnalysis.shortestCompletePathLength} steps`);
    console.log(`   - Quality distribution: High (8-10): ${analysis.scoreAnalysis.scoreDistribution['8-10']}`);
    console.log(`   - Average branching factor: ${analysis.branchingAnalysis.averageBranchingFactor.toFixed(1)}`);

    if (analysis.recommendations.length > 0) {
      console.log('\n💡 Process Improvement Recommendations:');
      analysis.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    // Save interactive visualization
    const htmlViz = visualizer.generateInteractiveHTML(planningResult.tree);
    const filename = `gemini_strategic_planning_${Date.now()}.html`;
    fs.writeFileSync(filename, htmlViz);
    console.log(`\n💾 Interactive visualization saved: ${filename}`);

    // Show strategic initiatives created
    if (planningResult.jobs && planningResult.jobs.length > 0) {
      console.log('\n📋 Strategic Initiatives Generated:');
      planningResult.jobs.forEach((job, index) => {
        console.log(`\n${index + 1}. ${job.brief.taskName}`);
        console.log(`   📝 ${job.brief.overview}`);
        console.log(`   🎯 Action: ${job.brief.action || 'Strategic analysis'}`);
        console.log(`   📊 Quality: ${job.brief.score ? job.brief.score.toFixed(1) : 'N/A'}/10`);
      });
    }

    // Performance comparison
    console.log('\n⚖️  Comparing with Traditional Planning:');
    const comparison = await agency.comparePlanningApproaches('gemini-strategic-agent', goal);
    
    console.log(`📋 Traditional approach: ${comparison.traditional.jobCount} tasks, ${comparison.traditional.planningTime}ms`);
    console.log(`🧠 Reflective approach: ${comparison.reflective.jobCount} tasks, ${comparison.reflective.planningTime}ms`);
    console.log(`⚡ Quality improvement: ${comparison.comparison.reflectiveAdvantage ? 'Significant' : 'Moderate'}`);
    console.log(`🎯 Depth advantage: ${comparison.reflective.treeStatistics ? comparison.reflective.treeStatistics.maxDepth : 0} reasoning levels`);

    console.log('\n🎉 Gemini-powered strategic planning demonstration completed!');
    console.log('\n🌟 Key Benefits Demonstrated:');
    console.log('   ✅ Systematic exploration of strategic options');
    console.log('   ✅ Quality scoring of each strategic decision');
    console.log('   ✅ Comprehensive reasoning documentation');
    console.log('   ✅ Interactive visualization for stakeholder review');
    console.log('   ✅ Integration with existing Agency.js workflows');

    return {
      planningResult,
      analysis,
      comparison,
      filename,
      performance: {
        planningTime,
        treeNodes: planningResult.tree.statistics.totalNodes,
        efficiency: planningResult.tree.statistics.explorationEfficiency
      }
    };

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    if (error.message.includes('API key')) {
      console.log('💡 Make sure your Gemini API key is valid and has sufficient quota');
    }
    throw error;
  }
}

// Export for use in other modules
export {
  runGeminiReflectiveDemo,
  createRealWorldTools
};

// Run demo if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runGeminiReflectiveDemo()
    .then(() => {
      console.log('\n✨ Demo completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Demo failed:', error);
      process.exit(1);
    });
}
