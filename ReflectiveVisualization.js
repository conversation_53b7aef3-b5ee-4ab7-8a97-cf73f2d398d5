/**
 * Visualization and debugging tools for ReflectiveAgent reasoning trees
 * Provides multiple formats for understanding and analyzing tree-of-thought processes
 */

/**
 * Tree Visualizer class for creating various representations of reasoning trees
 */
export class TreeVisualizer {
  constructor(options = {}) {
    this.options = {
      maxNodeTextLength: options.maxNodeTextLength || 60,
      showScores: options.showScores !== false,
      showTimestamps: options.showTimestamps || false,
      showActions: options.showActions !== false,
      colorizeByScore: options.colorizeByScore || false,
      ...options
    };
  }

  /**
   * Generate a detailed text-based tree visualization
   * @param {Object} tree - The serialized reasoning tree
   * @returns {string} - Formatted tree visualization
   */
  generateTextTree(tree) {
    if (!tree || !tree.nodes) {
      return 'No tree data available';
    }

    const nodes = tree.nodes;
    const rootNode = nodes[tree.rootId];
    
    if (!rootNode) {
      return 'Invalid tree structure - no root node found';
    }

    let output = `🌳 Reasoning Tree for: "${tree.goal}"\n`;
    output += `📊 Statistics: ${tree.statistics.totalNodes} nodes, depth ${tree.statistics.maxDepth}, efficiency ${(tree.statistics.explorationEfficiency * 100).toFixed(1)}%\n`;
    output += `⏱️  Duration: ${this._formatDuration(tree.startTime, tree.endTime)}\n\n`;
    
    output += this._renderNode(rootNode, nodes, 0, new Set());
    
    return output;
  }

  /**
   * Generate a Mermaid diagram representation of the tree
   * @param {Object} tree - The serialized reasoning tree
   * @returns {string} - Mermaid diagram code
   */
  generateMermaidDiagram(tree) {
    if (!tree || !tree.nodes) {
      return 'graph TD\n    A[No tree data available]';
    }

    const nodes = tree.nodes;
    let mermaid = 'graph TD\n';
    
    // Add nodes
    Object.values(nodes).forEach(node => {
      const nodeId = this._sanitizeId(node.id);
      const label = this._truncateText(node.thought, 30);
      const style = this._getMermaidNodeStyle(node);
      
      mermaid += `    ${nodeId}["${label}"]\n`;
      
      if (style) {
        mermaid += `    ${nodeId} --> ${nodeId}\n`;
        mermaid += `    class ${nodeId} ${style}\n`;
      }
    });

    // Add edges
    Object.values(nodes).forEach(node => {
      if (node.parentId) {
        const nodeId = this._sanitizeId(node.id);
        const parentId = this._sanitizeId(node.parentId);
        mermaid += `    ${parentId} --> ${nodeId}\n`;
      }
    });

    // Add styling
    mermaid += '\n    classDef complete fill:#90EE90\n';
    mermaid += '    classDef failed fill:#FFB6C1\n';
    mermaid += '    classDef executing fill:#FFE4B5\n';
    mermaid += '    classDef pending fill:#E6E6FA\n';

    return mermaid;
  }

  /**
   * Generate a JSON representation suitable for web visualization libraries
   * @param {Object} tree - The serialized reasoning tree
   * @returns {Object} - JSON structure for visualization libraries like D3.js
   */
  generateD3Json(tree) {
    if (!tree || !tree.nodes) {
      return { name: 'No data', children: [] };
    }

    const nodes = tree.nodes;
    const rootNode = nodes[tree.rootId];
    
    return this._convertToD3Node(rootNode, nodes);
  }

  /**
   * Generate a detailed analysis report of the reasoning tree
   * @param {Object} tree - The serialized reasoning tree
   * @returns {Object} - Comprehensive analysis report
   */
  generateAnalysisReport(tree) {
    if (!tree || !tree.nodes) {
      return { error: 'No tree data available' };
    }

    const nodes = Object.values(tree.nodes);
    const completedNodes = nodes.filter(n => n.state === 'complete');
    const failedNodes = nodes.filter(n => n.state === 'failed');
    const pendingNodes = nodes.filter(n => n.state === 'pending');

    // Calculate path analysis
    const paths = this._extractAllPaths(tree);
    const longestPath = paths.reduce((longest, current) => 
      current.length > longest.length ? current : longest, []);
    const shortestCompletePath = paths
      .filter(path => path[path.length - 1].state === 'complete')
      .reduce((shortest, current) => 
        current.length < shortest.length ? current : shortest, longestPath);

    // Score analysis
    const scores = nodes.map(n => n.score).filter(s => s > 0);
    const avgScore = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
    const maxScore = Math.max(...scores, 0);
    const minScore = Math.min(...scores, 10);

    // Branching analysis
    const branchingFactors = nodes.map(n => n.childrenIds ? n.childrenIds.length : 0);
    const avgBranching = branchingFactors.reduce((a, b) => a + b, 0) / branchingFactors.length;

    return {
      overview: {
        goal: tree.goal,
        totalNodes: nodes.length,
        completedNodes: completedNodes.length,
        failedNodes: failedNodes.length,
        pendingNodes: pendingNodes.length,
        maxDepth: tree.statistics.maxDepth,
        efficiency: tree.statistics.explorationEfficiency,
        duration: this._formatDuration(tree.startTime, tree.endTime)
      },
      pathAnalysis: {
        totalPaths: paths.length,
        longestPathLength: longestPath.length,
        shortestCompletePathLength: shortestCompletePath.length,
        averagePathLength: paths.reduce((sum, path) => sum + path.length, 0) / paths.length
      },
      scoreAnalysis: {
        averageScore: avgScore,
        maxScore: maxScore,
        minScore: minScore,
        scoreDistribution: this._calculateScoreDistribution(scores)
      },
      branchingAnalysis: {
        averageBranchingFactor: avgBranching,
        maxBranching: Math.max(...branchingFactors),
        branchingDistribution: this._calculateBranchingDistribution(branchingFactors)
      },
      recommendations: this._generateRecommendations(tree, {
        avgScore,
        avgBranching,
        efficiency: tree.statistics.explorationEfficiency,
        completionRate: completedNodes.length / nodes.length
      })
    };
  }

  /**
   * Generate an interactive HTML visualization
   * @param {Object} tree - The serialized reasoning tree
   * @returns {string} - HTML content with embedded visualization
   */
  generateInteractiveHTML(tree) {
    const d3Data = this.generateD3Json(tree);
    const mermaidDiagram = this.generateMermaidDiagram(tree);
    const analysisReport = this.generateAnalysisReport(tree);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reasoning Tree Visualization</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .tree-node { cursor: pointer; }
        .tree-node:hover { opacity: 0.8; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f5f5f5; padding: 15px; border-radius: 5px; text-align: center; }
        .mermaid { text-align: center; }
        pre { background: #f8f8f8; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 Reasoning Tree Analysis</h1>
        <p><strong>Goal:</strong> ${tree.goal}</p>
        
        <div class="section">
            <h2>📊 Overview Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>${analysisReport.overview.totalNodes}</h3>
                    <p>Total Nodes</p>
                </div>
                <div class="stat-card">
                    <h3>${analysisReport.overview.maxDepth}</h3>
                    <p>Max Depth</p>
                </div>
                <div class="stat-card">
                    <h3>${(analysisReport.overview.efficiency * 100).toFixed(1)}%</h3>
                    <p>Efficiency</p>
                </div>
                <div class="stat-card">
                    <h3>${analysisReport.scoreAnalysis.averageScore.toFixed(1)}</h3>
                    <p>Avg Score</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🗺️ Tree Diagram</h2>
            <div class="mermaid">
                ${mermaidDiagram}
            </div>
        </div>

        <div class="section">
            <h2>📈 Detailed Analysis</h2>
            <pre>${JSON.stringify(analysisReport, null, 2)}</pre>
        </div>

        <div class="section">
            <h2>🌲 Tree Structure (D3 Data)</h2>
            <div id="d3-tree"></div>
            <pre>${JSON.stringify(d3Data, null, 2)}</pre>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ startOnLoad: true });
        
        // D3 Tree Visualization (basic implementation)
        const treeData = ${JSON.stringify(d3Data)};
        
        // You can add more sophisticated D3 visualization here
        console.log('Tree data loaded:', treeData);
    </script>
</body>
</html>`;
  }

  // Private helper methods

  _renderNode(node, allNodes, depth, visited) {
    if (visited.has(node.id)) {
      return '  '.repeat(depth) + '↻ [Circular reference]\n';
    }
    
    visited.add(node.id);
    
    const indent = '  '.repeat(depth);
    const status = this._getStatusIcon(node.state);
    const score = this.options.showScores ? ` [${node.score.toFixed(1)}]` : '';
    const timestamp = this.options.showTimestamps ? ` (${new Date(node.timestamp).toLocaleTimeString()})` : '';
    
    let text = node.thought;
    if (text.length > this.options.maxNodeTextLength) {
      text = text.substring(0, this.options.maxNodeTextLength) + '...';
    }
    
    let result = `${indent}${status}${score} ${text}${timestamp}\n`;
    
    if (this.options.showActions && node.action) {
      result += `${indent}    🎯 Action: ${node.action}\n`;
    }
    
    if (node.result && typeof node.result === 'object' && node.result.error) {
      result += `${indent}    ❌ Error: ${node.result.error}\n`;
    }
    
    // Render children
    if (node.childrenIds && node.childrenIds.length > 0) {
      node.childrenIds.forEach(childId => {
        const childNode = allNodes[childId];
        if (childNode) {
          result += this._renderNode(childNode, allNodes, depth + 1, new Set(visited));
        }
      });
    }
    
    visited.delete(node.id);
    return result;
  }

  _getStatusIcon(state) {
    switch (state) {
      case 'complete': return '✅';
      case 'failed': return '❌';
      case 'executing': return '⏳';
      case 'pending': return '⏸️';
      default: return '❓';
    }
  }

  _getMermaidNodeStyle(node) {
    switch (node.state) {
      case 'complete': return 'complete';
      case 'failed': return 'failed';
      case 'executing': return 'executing';
      case 'pending': return 'pending';
      default: return null;
    }
  }

  _sanitizeId(id) {
    return id.replace(/[^a-zA-Z0-9]/g, '_');
  }

  _truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  _formatDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'Unknown';
    const duration = new Date(endTime) - new Date(startTime);
    return `${(duration / 1000).toFixed(1)}s`;
  }

  _convertToD3Node(node, allNodes) {
    const d3Node = {
      name: this._truncateText(node.thought, 40),
      id: node.id,
      score: node.score,
      state: node.state,
      action: node.action,
      depth: node.depth,
      children: []
    };

    if (node.childrenIds && node.childrenIds.length > 0) {
      d3Node.children = node.childrenIds.map(childId => {
        const childNode = allNodes[childId];
        return childNode ? this._convertToD3Node(childNode, allNodes) : null;
      }).filter(Boolean);
    }

    return d3Node;
  }

  _extractAllPaths(tree) {
    const paths = [];
    const nodes = tree.nodes;
    const rootNode = nodes[tree.rootId];
    
    const extractPaths = (node, currentPath) => {
      const newPath = [...currentPath, node];
      
      if (!node.childrenIds || node.childrenIds.length === 0) {
        paths.push(newPath);
      } else {
        node.childrenIds.forEach(childId => {
          const childNode = nodes[childId];
          if (childNode) {
            extractPaths(childNode, newPath);
          }
        });
      }
    };
    
    extractPaths(rootNode, []);
    return paths;
  }

  _calculateScoreDistribution(scores) {
    const buckets = { '0-2': 0, '2-4': 0, '4-6': 0, '6-8': 0, '8-10': 0 };
    scores.forEach(score => {
      if (score < 2) buckets['0-2']++;
      else if (score < 4) buckets['2-4']++;
      else if (score < 6) buckets['4-6']++;
      else if (score < 8) buckets['6-8']++;
      else buckets['8-10']++;
    });
    return buckets;
  }

  _calculateBranchingDistribution(branchingFactors) {
    const distribution = {};
    branchingFactors.forEach(factor => {
      distribution[factor] = (distribution[factor] || 0) + 1;
    });
    return distribution;
  }

  _generateRecommendations(tree, metrics) {
    const recommendations = [];
    
    if (metrics.efficiency < 0.5) {
      recommendations.push('Consider reducing exploration breadth - efficiency is low');
    }
    
    if (metrics.avgScore < 6.0) {
      recommendations.push('Review scoring criteria - average scores are low');
    }
    
    if (metrics.avgBranching > 4) {
      recommendations.push('Consider reducing max branches to improve focus');
    }
    
    if (metrics.completionRate < 0.3) {
      recommendations.push('Many nodes remain incomplete - consider increasing max depth');
    }
    
    if (tree.statistics.maxDepth < 3) {
      recommendations.push('Shallow exploration - consider allowing deeper reasoning');
    }
    
    return recommendations;
  }
}

/**
 * Debug utilities for ReflectiveAgent
 */
export class ReflectiveDebugger {
  constructor(agent) {
    this.agent = agent;
    this.visualizer = new TreeVisualizer();
  }

  /**
   * Generate a comprehensive debug report
   * @returns {Object} - Debug information
   */
  generateDebugReport() {
    const currentTree = this.agent.getCurrentTree();
    const memoryContents = this._getMemorySnapshot();
    const agentState = this._getAgentState();
    
    return {
      timestamp: new Date(),
      agentState,
      currentTree,
      memoryContents,
      treeAnalysis: currentTree ? this.visualizer.generateAnalysisReport(currentTree) : null,
      recommendations: this._generateDebugRecommendations(agentState, currentTree)
    };
  }

  /**
   * Export tree visualization to file
   * @param {string} format - Format: 'html', 'mermaid', 'json', 'text'
   * @returns {string} - Formatted output
   */
  exportVisualization(format = 'html') {
    const tree = this.agent.getCurrentTree();
    if (!tree) return 'No active tree to visualize';
    
    switch (format.toLowerCase()) {
      case 'html':
        return this.visualizer.generateInteractiveHTML(tree);
      case 'mermaid':
        return this.visualizer.generateMermaidDiagram(tree);
      case 'json':
        return JSON.stringify(this.visualizer.generateD3Json(tree), null, 2);
      case 'text':
        return this.visualizer.generateTextTree(tree);
      default:
        throw new Error(`Unsupported format: ${format}`);
    }
  }

  _getMemorySnapshot() {
    return {
      conversationHistory: this.agent.memory.getHistory(5), // Last 5 entries
      keyValueStore: { ...this.agent.memory.keyValueStore },
      reasoningPatterns: this.agent.getSimilarReasoningPatterns('').slice(0, 3) // Last 3 patterns
    };
  }

  _getAgentState() {
    return {
      id: this.agent.id,
      name: this.agent.name,
      status: this.agent.status,
      toolCount: Object.keys(this.agent.tools).length,
      goals: this.agent.goals,
      config: {
        maxDepth: this.agent.maxDepth,
        maxBranches: this.agent.maxBranches,
        explorationStrategy: this.agent.explorationStrategy,
        scoringThreshold: this.agent.scoringThreshold
      }
    };
  }

  _generateDebugRecommendations(agentState, tree) {
    const recommendations = [];
    
    if (!tree) {
      recommendations.push('No active reasoning tree - agent may not be in reasoning mode');
    }
    
    if (agentState.status === 'error') {
      recommendations.push('Agent is in error state - check recent operations');
    }
    
    if (agentState.toolCount === 0) {
      recommendations.push('No tools available - consider adding relevant tools for better reasoning');
    }
    
    return recommendations;
  }
}
